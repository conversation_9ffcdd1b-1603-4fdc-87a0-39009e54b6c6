<?php

namespace InvatoCore\Market\Controllers;

use Backend;
use Backend\Behaviors\FormController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendAuth;
use BackendMenu;
use Flash;
use Illuminate\Http\RedirectResponse;
use invatocore\market\console\Traits\CanHandlePluginsTrait;
use InvatoCore\Market\Jobs\FullRefreshMarketPluginsJob;
use October\Rain\Flash\FlashBag;
use Redirect;

/**
 * Class Plugins
 */
class MarketPlugins extends Controller
{
    use CanHandlePluginsTrait;

    public $implement = [
        ListController::class,
        FormController::class,
    ];

    public string $listConfig = 'config_list.yaml';

    public string $formConfig = 'config_form.yaml';

    public $requiredPermissions = [
        'invatocore.market.manage_plugin',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('InvatoCore.Market', 'market', 'marketplugins');
    }

    public function create(): RedirectResponse
    {
        // Redirect naar de lijstweergave
        return Redirect::to(Backend::url('invatocore/market/marketplugins'));
    }

    public function onRefreshPlugins()
    {
        FullRefreshMarketPluginsJob::dispatch();

        Flash::add(FlashBag::INFO, e(trans('invatocore.market::lang.flash.refresh_queued')));

        return $this->listRefresh();
    }

    public function onRestartQueues()
    {
        $this->restartQueues();

        Flash::add(FlashBag::INFO, $this->flashInfo);

        return $this->listRefresh();
    }

    public function onDumpAutoloader()
    {
        $this->dumpAutoload();

        Flash::add(FlashBag::INFO, $this->flashInfo);

        return $this->listRefresh();
    }

    public function onMigrateDatabase()
    {
        $this->migrateDatabase();

        Flash::add(FlashBag::WARNING, $this->flashInfo);

        return $this->listRefresh();
    }

    public function formExtendFields($form)
    {
        // Haal de huidige gebruiker op
        $user = BackendAuth::getUser();

        // Controleer of de gebruiker supergebruiker is of de rechtengroep ID 2 heeft
        if (
            ! $user->isSuperUser()
            &&
            $user->group_id != 2
        ) {
            // Als de gebruiker geen toegang heeft, schakel dan alle velden uit
            $form->fields->each(function ($field) {
                $field->disabled = true;
            });

            // Toon een melding dat de gebruiker geen toegang heeft
            Flash::error(e(trans('invatocore.market::lang.flash.no_permission')));
        }
    }

    public function update($recordId = null)
    {
        return Redirect::to(Backend::url('invatocore/market/marketplugins/preview/'.$recordId));
    }
}
