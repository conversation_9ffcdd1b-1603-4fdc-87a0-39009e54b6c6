<?php

namespace InvatoCore\Market\Console;

use Illuminate\Console\Command;
use InvatoCore\Market\Console\Traits\CanHandlePluginsTrait;

/**
 * Class CreateSymlinksCommand
 */
class RemoveSymlinksCommand extends Command
{
    use CanHandlePluginsTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'market:unlink';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove symlinks for all available market items';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->unlinkPlugins();
        $this->dumpAutoload();
    }
}
