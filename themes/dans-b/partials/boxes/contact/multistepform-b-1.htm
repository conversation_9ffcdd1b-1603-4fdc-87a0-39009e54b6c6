[renderForm multistepform]
formCode = "multistep-formulier"
==
<section class="" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        <div class="relative bg-white max-w-5xl mx-auto md:grid md:grid-cols-4 {{ this.theme.design.border_radius }} border border-gray-100 shadow-xl">
            <div class="hidden md:block relative col-span-1 w-full h-full overflow-hidden">
                {% if this.theme.design.border_radius == 'rounded-2xl' %}
                {% set imgRadius = 'rounded-l-2xl' %}
                {% elseif this.theme.design.border_radius == 'rounded-lg' %}
                {% set imgRadius = 'rounded-l-lg' %}
                {% elseif this.theme.design.border_radius == 'rounded' %}
                {% set imgRadius = 'rounded-l' %}
                {% else %}
                {% set imgRadius = 'rounded-none' %}
                {% endif %}
                <img src="{{ box.img | media | resize(600, auto, { 'extension': 'webp' }) }}" class="{{ imgRadius }} w-full h-full object-cover" alt="{{ box.img_title }}">
                {% if box.overlay == 'overlay' %}
                <div class="absolute inset-0 bg-primary-800/30 {{ imgRadius }}"></div>
                {% endif %}
            </div>
            <div class="relative md:col-span-3 py-12 px-4 md:px-8 lg:px-16 xl:px-24">
                <div class="pb-6 mx-4">
                    <h3 class="text-gray-800">{{ box.title }}</h2>
                </div>
                <div class="">
                    <div id="form_default">
                        {% component 'multistepform' %}
                    </div>
                </div>
            </div>
            <div class="absolute top-0 right-0 overflow-hidden translate-x-[1px] -translate-y-[1px]">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120"
                     style="enable-background:new 0 0 120 120; color: {{ box.background_color|default('#ffffff') }};"
                     class="fill-primary-700 w-20 xl:w-28" xml:space="preserve">
                        <g>
                            <path class="st0" d="M120,0H0l120,120"/>
                            <path class="st0" d="M-33.9,9.1"/>
                        </g>
                    </svg>
            </div>
        </div>
    </div>
</section>
