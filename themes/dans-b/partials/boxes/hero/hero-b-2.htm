<div class="relative h-full lg:h-[920px] overflow-hidden" style="background-color: {{ box.background_color }};">
    {% if box.video %}
    <video autoplay muted loop preload playsinline src="{{ box.video|media }}" class="absolute inset-0 w-full h-full object-cover"></video>

    <div class="absolute inset-0 overflow-hidden">
        <div class="bg-primary-800/30 w-full h-full"></div>
    </div>
    {% endif %}

    <div class="relative flex items-center justify-center h-full w-full z-20 mt-32 mb-20 lg:mt-0 lg:mb-0" data-boxes-container data-rel="boxes-wrapper">
        <div class="container">
            <div class="flex flex-col items-center space-y-6 lg:space-y-12">
                {% if box.title %}
                <div class="text-center">
                    <h1 class="text-white">{{ box.title | content }}</h1>
                </div>
                {% endif %}
                <div class="content_section_hero max-w-xl text-center text-white drop-shadow-md">
                    {{ box.content | content }}
                </div>
                {% if box.buttons %}
                <div class="flex flex-wrap items-center justify-center space-y-4 md:space-y-0 md:space-x-12">
                    {% for button in box.buttons %}
                    {% partial "ui/button" item = button %}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="absolute inset-x-0 bottom-0 translate-y-[1px]">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
             viewBox="0 0 1920 100" class="fill-current w-full" style="enable-background:new 0 0 1920 100; color: #ffffff;" xml:space="preserve">
            <g>
                <path class="st0" d="M0,100L0,100L0,100h1920V0L0,100z"/>
            </g>
        </svg>
    </div>
</div>
