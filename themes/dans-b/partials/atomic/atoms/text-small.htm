<p class="text-sm text-gray-500 dark:text-gray-200 {{ is_dark_bg ? 'dark:text-gray-200' : 'dark:text-gray-500' }} {{ text_sm_class }}">
    {% if truncate %}
        {{ text | str_limit(truncate, '...') | raw }}
    {% else %}
        {{ text | raw }}
    {% endif %}
</p>

{#
    accepted variables:

    'text'
        - text to display
        - default: null
        - accept html: no

    'truncate'
        - max. characters in text variable
        - type: number
        - default: null

    'text_sm_class'
        - CSS class, can be Tailwind CSS classes
        - default: null
#}
