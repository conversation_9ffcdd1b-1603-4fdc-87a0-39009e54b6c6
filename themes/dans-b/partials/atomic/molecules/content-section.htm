{% import 'macros/shortcodes' as shortcode %}

{% set contentText = shortcode.company_info(content, company) %}

{% if truncate %}
    {{ contentText | content | html_limit(truncate, '...') }}
{% else %}
    {{ contentText | content }}
{% endif %}
{#
    accepted variables:

    'content'
        - content to show
        - accepts html: yes
        - accepts markdown: no
        - default: null

    'truncate'
        - maximum of characters in content
        - integer field
        - default: null
#}
