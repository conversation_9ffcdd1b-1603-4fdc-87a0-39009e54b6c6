<li role="presentation" class="relative group flex flex-wrap w-full items-center {{ item.viewBag.cssClass }}" x-data="{ submenu: false }">
{% if item.url %}
    <div class="w-full text-gray-800 {{ item.isActive ? 'text-primary-500' }}">
        <a href="{{ item.url }}" class="leading-none font-semibold py-5" {{ item.viewBag.isExternal ? 'target="_blank"' }}>
            {{ item.title }}
        </a>
        {% if item.items %}
            <button type="button" @click="submenu = !(submenu)" class="px-2">
                <i class="fa-regular fa-angle-down" x-show="!(submenu)"></i>
                <i class="fa-regular fa-angle-up" x-show="submenu" x-cloak></i>
            </button>
        {% endif %}
    </div>
{% else %}
    <div class="w-full text-gray-800 {{ item.isActive ? 'text-primary-500' }}">
        <span href="{{ item.url }}" class="leading-none font-semibold py-5" {{ item.viewBag.isExternal ? 'target="_blank"' }} @click="submenu = !(submenu)">
            {{ item.title }}
        </span>
        {% if item.items %}
            <button type="button" @click="submenu = !(submenu)" class="px-2">
                <i class="fa-regular fa-angle-down" x-show="!(submenu)"></i>
                <i class="fa-regular fa-angle-up" x-show="submenu" x-cloak></i>
            </button>
        {% endif %}
    </div>
{% endif %}

{% if item.items %}
    <div class="flex w-full" x-show="submenu" x-cloak x-collapse>
        <div class="text-sm font-semibold leading-6 text-gray-800 mt-4 pl-3 space-y-3">
            {% for sub in item.items %}
                <a href="{{ sub.url }}" class="block hover:text-primary-500 {{ sub.isActive ? 'text-primary-500' }}" {{ sub.viewBag.isExternal ? 'target="_blank"' }}>{{ sub.title }}</a>
            {% endfor %}
        </div>
    </div>
{% endif %}
</li>
