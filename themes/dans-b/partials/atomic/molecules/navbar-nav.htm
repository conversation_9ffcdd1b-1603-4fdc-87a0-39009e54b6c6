[staticMenu mainMenu]
code = "main-menu"
==
{% set mainmenuItems = mainMenu.resetMenu(this.theme.navbar.site_primary_menu) %}
<nav id="navbar-nav">
    <ul class="flex flex-wrap space-x-4 xl:gap-x-6">
        {% for item in mainmenuItems %}
            {% if not item.viewBag.isHidden %}
                <li role="presentation" class="relative group {{ item.viewBag.cssClass }}">
                    {% if item.type == 'header' %}
                        <span class="leading-none text-lg text-gray-900 font-semibold py-5 hover:opacity-100 hover:text-primary">
                            {{ item.title }}
                        </span>
                        {% if item.items %}
                            <i class="fa-regular fa-angle-down"></i>
                        {% endif %}
                    {% else %}
                        <a href="{{ item.url }}" class="leading-none text-lg text-gray-900 font-semibold py-5 {{ item.isActive ? 'opacity-100' : 'opacity-90' }} hover:opacity-100 hover:text-primary" {{ item.viewBag.isExternal ? 'target="_blank"' }}>
                            {{ item.title }}
                        </a>
                        {% if item.items %}
                            <i class="fa-regular fa-angle-down"></i>
                        {% endif %}
                    {% endif %}

                    {% if item.items %}
                        <div class="hidden group-hover:flex absolute left-1/2 z-10 mt-3 w-screen max-w-min -translate-x-1/2 px-4">
                            <div class="w-56 shrink rounded-xl bg-white p-4 text-sm font-semibold leading-6 text-gray-900 shadow-lg ring-1 ring-gray-900/5">
                                {% for sub in item.items %}
                                    <a href="{{ sub.url }}" class="block p-2 hover:text-primary-500 {{ sub.isActive ? 'text-primary-500' }}" {{ sub.viewBag.isExternal ? 'target="_blank"' }}>{{ sub.title }}</a>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </li>
            {% endif %}
        {% endfor %}
    </ul>
</nav>
