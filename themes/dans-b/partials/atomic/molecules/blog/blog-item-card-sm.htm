[postList]
==

<div class="relative blog-item-card group/card flex rounded-md overflow-hidden h-full">

    <div class="absolute p-4 flex flex-wrap gap-2 z-20">
        {% for cat in item.categories %}
            {% partial 'atomic/atoms/batch-link-small' link=categoryPage | page({ slug: cat.slug }) label=cat.title %}
        {% endfor %}
    </div>

    {% if item.thumbnail_image %}
        {% set itemImage = item.thumbnail_image %}
        {% set imgDescription = item.thumb_img_title %}
    {% else %}
        {% set itemImage = item.image %}
        {% set imgDescription = item.img_title %}
    {% endif %}

    <div class="absolute inset-0 w-full h-full overflow-hidden">
        {% partial 'atomic/atoms/media/image' img=itemImage title=imgDescription resize_w='500' class='w-full h-full object-cover group-hover/card:scale-110 transition-all duration-500' %}
        <a href="{{ postPage | page({ slug: item.slug }) }}" title="{{ item.title }}" class="absolute inset-0"></a>
    </div>

    <div class="absolute inset-0 w-full h-full overflow-hidden">
        <div class="w-full h-full bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
        <a href="{{ postPage | page({ slug: item.slug }) }}" title="{{ item.title }}" class="absolute inset-0"></a>
    </div>

    <div class="relative dark blog-item-content flex flex-col h-fit mt-auto flex-1 gap-y-6">

        <div class="blog-item-title prose prose-primary dark:prose-primary_inverted max-w-none">

            {% if item.title_short %}
                {% set blogTitle = item.title_short %}
            {% else %}
                {% set blogTitle = item.title %}
            {% endif %}

            {% partial 'atomic/atoms/cards/card-heading' text=blogTitle class='text-lg' %}
        </div>

        <div class="blog-item-info mt-auto">
            <div class="flex flex-wrap justify-between w-full gap-1">
                {% if postList.showAuthor %}
                    {% partial 'atomic/atoms/blog/author' class='text-xs' firstName=item.author.first_name lastName=item.author.last_name %}
                {% endif %}
                {% if postList.showDates %}
                    {% partial 'atomic/atoms/blog/date' date=item.publication_date class='ml-auto text-xs' %}
                {% endif %}
            </div>
        </div>

        <a href="{{ postPage | page({ slug: item.slug }) }}" title="{{ item.title }}" class="absolute inset-0"></a>

    </div>

</div>
