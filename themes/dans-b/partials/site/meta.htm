<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">

{% if boxesPage %}
    <title>{{ boxesPage.og_title | default(boxesPage.meta_title) | default(boxesPage.name) }}</title>
    <meta name="description" content="{{ boxesPage.meta_description }}">
{% else %}
    <title>{{ this.page.meta_title | default(this.page.title) }}{{ this.theme.seo.meta_title_suffix ? ' ' ~ this.theme.seo.meta_title_suffix : '' }}</title>
{% endif %}

{% put meta %}
    {% if this.theme.seo.additional_meta_tags %}{{ this.theme.seo.additional_meta_tags|raw }} {% endif %}
{% endput %}
