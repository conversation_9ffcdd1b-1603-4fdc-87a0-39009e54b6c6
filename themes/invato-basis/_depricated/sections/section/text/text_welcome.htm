<section class="py-8 md:py-12 lg:py-16" style="background-color: {{ fields.background_color }};">
    <div class="container px-4 xl:px-32">
        <div class="md:grid md:grid-cols-3 md:gap-6 lg:gap-y-10 xl:gap-y-16">
            <div class="md:col-span-2 text-gray-700">
                <h2 class="text-primary-500 text-3xl md:text-4xl font-bold">{{ fields.title }}</h2>
                <h2 class="text-secondary-400 text-base mb-6">{{ fields.subtitle }}</h2>
                <div class="content_section prose-sm">
                    {{ fields.text|content }}
                </div>
                {% if fields.buttons %}
                <div class="flex flex-wrap items-center justify-center md:justify-start align-items my-3 lg:my-6 space-x-0 md:space-x-6 space-y-3 md:space-y-0">
                    {% for button in fields.buttons %}
                    {% partial 'ui/button' url=button.url size=button.size style=button.style icon=button.icon
                    text=button.text target_blank=button.target_blank %}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="py-3 md:py-0">
                <img src="{{ fields.image|media|resize(900, auto, { 'extension': 'webp' }) }}" class="rounded-3xl shadow-os shadow-black/40" alt="{{ fields.title }}">
            </div>
            <div class="logo_text text-base md:text-lg lg:text-xl py-2 md:py-0">
                {{ fields.text_logos|raw }}
            </div>
            <div class="md:col-span-2">
                <div class="flex flex-wrap space-x-4">
                    {% for item in fields.logos %}
                    <div class="relative">
                        {% if item.url %}
                        <a href="{{ item.url }}" title="{{ item.title }}" class="peer absolute inset-0"></a>
                        {% endif %}
                        <img src="{{ item.logo|media|resize(300, auto) }}" alt="{{ item.description }}" class="h-6 grayscale peer-hover:grayscale-0 hover:grayscale-0 transition-all">
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>