description = "Homepagina - DES"

[staticPage]
useContent = 0
default = 0

[staticBreadcrumbs]

[sitePicker]
==
<!doctype html>
<html lang="en">
    <head>
        <title>{{ this.page.title }}</title>
        {% partial "site/head" %}
        {% styles %}
    </head>
    <body>
        {% partial "site/scripts_body_top" %}
        {% page  %}
        {% partial "page/navbars/" ~ this.theme.navbar|default('navbar-1') ~ ".htm" %}

        {variable tab="Header" name="title_style" label="Titel Opmaak" span="row" spanClass="col-sm-6" type="balloon-selector" default="single_color" options="single_color: 1 kleur | multi_color: 2 kleuren"}{/variable}
        {variable tab="Header" name="image" label="Afbeelding" span="row" spanClass="col-sm-4" type="mediafinder"}{/variable}
        {variable tab="Header" name="title_1" label="Titel" span="row" spanClass="col-sm-6" type="text" }{/variable}
        {variable tab="Header" name="title_color" label="Titel toevoeging (in kleur)" span="row" spanClass="col-sm-6" type="text" }{/variable}
        {variable tab="Header" name="content" label="Tekst" span="row" spanClass="col-sm-12" type="richeditor" }{/variable}
        {variable tab="Header" name="buttons" label="Button" span="row" spanClass="col-sm-8" type="repeater" prompt="Voeg een button toe" maxItems="2" style="accordion" titleFrom="text" form="$/../themes/data-entry-solutions/repeaters/includes/button.yaml"}{/variable}

        <div class="bg-white">
            <div class="relative isolate overflow-hidden bg-gradient-to-b from-primary-100/30 lg:pt-14">
                <div class="absolute inset-y-0 right-1/2 -z-10 -mr-96 w-[200%] origin-top-right skew-x-[-30deg] bg-white shadow-xl shadow-primary-500/30 ring-1 ring-indigo-50 sm:-mr-80 lg:-mr-96" aria-hidden="true"></div>
                <div class="mx-auto max-w-7xl px-6 py-12 sm:py-24 lg:px-8">
                    <div class="mx-auto max-w-2xl lg:mx-0 lg:grid lg:max-w-none lg:grid-cols-2 lg:gap-x-16 lg:gap-y-6 xl:grid-cols-1 xl:grid-rows-1 xl:gap-x-8">
                        <h1 class="max-w-3xl text-4xl font-bold tracking-tight text-primary-500 sm:text-6xl lg:col-span-2 xl:col-auto">
                            <span class="text-primary-500 uppercase">{{ title_1 }}</span>
                            {% if title_style == 'multi_color' %}
                            <span class="block text-4xl text-secondary-500">{{ title_color }}</span>
                            {% endif %}
                        </h1>
                        <div class="mt-6 max-w-xl lg:mt-0 xl:col-end-1 xl:row-start-1">
                            <div class="text-lg leading-8 text-gray-600">{{ content|content }}</div>

                            {% if buttons %}
                            <div class="mt-10 flex items-center gap-x-12">
                                {% for button in buttons %}
                                {% partial 'ui/button' url=button.url size=button.size style=button.style icon=button.icon
                                text=button.text target_blank=button.target_blank %}
                                {% endfor %}
                            </div>
                            {% endif %}

                        </div>
                        <img src="{{ image|media|resize(1000, auto, { 'extension': 'webp' }) }}" alt="" class="mt-10 aspect-[6/5] w-full max-w-lg object-cover sm:mt-16 lg:mt-0 lg:max-w-none xl:row-span-2 xl:row-end-2 xl:mt-16 shadow-xl shadow-primary-500/30">
                    </div>
                </div>
                <div class="absolute inset-x-0 bottom-0 -z-10 h-24 bg-gradient-to-t from-white sm:h-32"></div>
            </div>
        </div>

        <main class="">



            {repeater tab="Content" name="flexible" prompt="Voeg sectie toe" groups="$/../themes/data-entry-solutions/repeaters/sections.yaml" displayMode="builder" titleFrom="name"}
                {% if 'banner_' in fields._group %}
                {% partial "section/banner/" ~ fields._group ~ ".htm" fields=fields %}
            {% elseif 'card_' in fields._group %}
            {% partial "section/card/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'cta_' in fields._group %}
                {% partial "section/cta/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'feature_' in fields._group %}
                {% partial "section/feature/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'hero_' in fields._group %}
                {% partial "section/hero/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'logos_' in fields._group %}
                {% partial "section/logos/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'snippet_' in fields._group %}
                {% partial "section/snippet/" ~ fields._group ~ ".htm" fields=fields %}
            {% elseif 'stats_' in fields._group %}
            {% partial "section/stats/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'text_' in fields._group %}
                {% partial "section/text/" ~ fields._group ~ ".htm" fields=fields %}
                {% else %}
                {% partial "section/" ~ fields._group ~ ".htm" fields=fields %}
                {% endif %}
            {/repeater}

        </main>
        {% if this.theme.navbar == 'navbar-3' %}
        {variable tab="Flyout menu" name="snippet_title" type="text" label="Title" span="left" permissions="developer"}{/variable}
        {variable tab="Flyout menu" name="snippet" type="richeditor" size="huge" label="Snippet" span="left" toolbarButtons="snippets|html" permissions="developer"}{/variable}
        {% endif %}

        {% partial "page/footers/" ~ this.theme.footer|default('footer-1') ~ ".htm" %}

        {% partial "page/cookie-alert" %}

        {% partial "site/foot" %}

        {% scripts %}
        {% framework extras %}
    </body>
</html>