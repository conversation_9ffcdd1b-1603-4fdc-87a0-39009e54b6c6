{% set reviews = ReviewList.reviews %}
<section class="reviews" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        <div class="text-center">
            <h2 class="reviews-title">{{ box.title }}</h2>
            <div class="reviews-intro">
                <div class="content_section">
                    {{ box.text | content }}
                </div>
            </div>
        </div>
        <div class="md:grid md:grid-cols-3 md:gap-8 mt-12">
            {% for item in reviews %}
                <div>
                    <div class="rounded-xl border border-gray-300 shadow-xl bg-white p-8">
                        <div class="flex flex-wrap items-start">
                            <div class="w-1/2">
                                <div class="text-xl font-bold">{{ item.name }}</div>
                                <div class="text-sm font-medium text-primary-600">{{ item.date|date('d-m-Y') }}</div>
                            </div>
                            <div class="w-1/2 flex justify-end">
                                <div class="text-lg ">
                                    {% for i in 1..item.score %}
                                        <i class="fa-solid fa-star text-amber-500"></i>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="content_section mt-6" x-data="{ expanded: false }">
                            <div :class="{ 'mb-4': !(expanded) }" x-show="expanded" x-collapse.min.136px>{{ item.review | content }}</div>
                            <button @click="expanded = ! expanded" class="font-bold text-primary-500 hover:underline">

                            {% if item.review|length > 190 %}
                            <span x-show="!(expanded)">Lees verder</span>
                            <span x-show="expanded">Lees minder</span>
                            {% endif %}

                            </button>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
