<section id="hero-2" class="hero relative text-white" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="absolute h-full w-full overflow-hidden">
        <img src="{{ box.background_img|media }}" class="w-full h-full object-cover" alt="">
    </div>

    <div class="relative h-full flex items-center">
        <div class="hidden md:block {{ box.invert == 'left' ? 'md:left-0' : 'md:right-0' }} relative overflow-hidden md:absolute h-full md:w-1/3 lg:w-1/2 ">
            <img src="{{ box.img|media|resize(1200, auto, { 'extension': 'webp' }) }}" class="h-full w-full object-cover lg:py-12 xl:py-16" alt="" style="transform: perspective(800px) rotateY(-5deg);">
        </div>

        <div class="relative container py-12 lg:py-32 xl:py-48">
            <div class="{{ box.invert == 'left' ? 'md:ml-auto md:pl-12 lg:pl-16 lg:pr-0 xl:pl-20' : 'md:mr-0 md:pr-12 lg:pr-16 lg:pl-0 xl:pr-20' }} flex flex-col space-y-8 md:w-2/3 lg:w-1/2 px-3">
                <div class="flex flex-col space-y-8">
                    <h2 class="text-primary-500">{{ box.keywords }}</h2>
                    <h1 class="font-heading text-white">{{ box.title }}</h1>
                </div>
                <div class="content_section text-lg text-white">
                    {{ box.content|content }}
                </div>

                {% if box.buttons %}
                <div class="flex flex-wrap align-items space-y-4 md:space-y-0 md:space-x-8 pt-6 md:pt-12 lg:pt-20">
                    {% for button in box.buttons %}
                    {% partial 'ui/button' url=button.url text=button.text btnSize=button.size btnStyle=button.style target_blank=button.target_blank icon=button.icon %}
                    {% endfor %}
                </div>
                {% endif %}

            </div>
        </div>
    </div>
</section>
