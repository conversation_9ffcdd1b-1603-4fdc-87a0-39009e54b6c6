{% set columns = "md:grid-cols-2" %}
{% if box.columns == "2" %}
    {% set columns = "md:grid-cols-2" %}
{% elseif box.columns == "3" %}
    {% set columns = "md:grid-cols-3" %}
{% elseif box.columns == "4" %}
    {% set columns = "md:grid-cols-4" %}
{% endif %}

{% if box.gap == "0" %}
    {% set gap = "md:gap-0" %}
{% elseif box.gap == "4" %}
    {% set gap = "md:gap-4" %}
{% elseif box.gap == "8" %}
    {% set gap = "md:gap-8" %}
{% elseif box.gap == "12" %}
    {% set gap = "md:gap-12" %}
{% elseif box.gap == "16" %}
    {% set gap = "md:gap-16" %}
{% else %}
    {% set gap = "md:gap-8" %}
{% endif %}

{% if box.py == "0" %}
    {% set py = "md:py-0" %}
{% elseif box.py == "8" %}
    {% set py = "md:py-8" %}
{% elseif box.py == "12" %}
    {% set py = "py-4 md:py-8 lg:py-12" %}
{% elseif box.py == "16" %}
    {% set py = "py-8 md:py-12 lg:py-16" %}
{% elseif box.py == "24" %}
    {% set py = "py-8 md:py-16 lg:py-24" %}
{% else %}
    {% set py = "md:py-16" %}
{% endif %}

{% set alignChild = "" %}
{% if box.align == "center" %}
    {% set align = "items-center" %}
{% elseif box.align == "bottom" %}
    {% set align = "items-end" %}
{% elseif box.align == "same_height" %}
    {% set align = "" %}
    {% set alignChild = "card-same-height" %}
{% else %}
    {% set align = "items-start" %}
{% endif %}

{% set dark = false %}
{% if box.bg_color == "#6b7280" or box.bg_color == "#374151" or box.bg_color == "#18181b" %}
    {% set dark = true %}
{% endif %}

{% set bgColor = '' %}

<section class="{{ dark ? 'dark' }} relative" style="background-color: {{ box.bg_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="absolute inset-0">
        {% if box.bg_overlay_color %}
            <div class="absolute inset-0 z-30 {{ box.bg_overlay_effect == 'blur' ? 'backdrop-blur-md' }}" style="background-color: {{ box.bg_overlay_color }}; opacity: .{{ box.bg_overlay_alpha }};"></div>
        {% endif %}
        {% for item in box.bg_image %}
            <img src="{{ item | media | resize(1920) }}" alt="" class="w-full h-full object-cover relative z-20">
        {% endfor %}
    </div>
    <div class="container relative z-30">
        <div class="md:grid {{ columns }} {{ gap }} {{ align }}">

            {% for child in box.children %}

                {% set colspan = "col-span-1" %}
                {% if child.col_span == "2" %}
                    {% set colspan = "col-span-2" %}
                {% elseif child.col_span == "3" %}
                    {% set colspan = "col-span-3" %}
                {% elseif child.col_span == "4" %}
                    {% set colspan = "col-span-4" %}
                {% endif %}

                {% set rowspan = "row-span-1" %}
                {% if child.row_span == "2" %}
                    {% set rowspan = "row-span-2" %}
                {% elseif child.row_span == "3" %}
                    {% set rowspan = "row-span-3" %}
                {% elseif child.row_span == "4" %}
                    {% set rowspan = "row-span-4" %}
                {% endif %}

                {% set bgColor = box.background_color %}

                <div class="{{ colspan }} {{ rowspan }} {{ alignChild }}">
                    {{ child.renderMergeContext(context, { loop: loop }) | raw }}
                </div>
            {% endfor %}

        </div>
    </div>
</section>
