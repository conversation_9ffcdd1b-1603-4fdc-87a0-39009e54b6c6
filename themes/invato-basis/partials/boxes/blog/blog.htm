{% set items = blogItems7.posts %}
{% set blogPage = blogItems7.blogPage|link %}
{% set postPage = blogItems7.postPage %}
{% set categoryPage = blogItems7.categoryPage %}


<section id="postlist-3" class="" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        <div class="text-center">
            <h2>Laatste blogberichten</h2>
        </div>

        <div class="row-grid md:grid-cols-4 md:gap-8 mt-16">
            {% for item in items %}
                <div class="blog-item blog-card {{ loop.first ? 'first' }} blog-item-{{ loop.index }}">
                    <div class="blog-item-thumb">
                        <div class="blog-item-categories">
                            {% for cat in item.categories %}
                                <a href="{{ categoryPage | page({ slug: cat.slug }) }}" title="{{ cat.title }}" class="blog-item-category">{{ cat.title }}</a>
                            {% endfor %}
                        </div>
                        <a href="{{ postPage | page({ slug: item.slug }) }}" title="{{ item.title }}">
                            <img src="{{ item.thumbnail.getPath() | resize(600) }}" alt="{{ item.thumbnail.description }}" class="blog-item-image">
                        </a>
                    </div>
                    <div class="blog-card-body">
                        <div class="blog-item-info">
                            <div class="blog-item-info-row">
                                {% if blogItems4.showDates %}
                                    <div class="blog-item-date">
                                        <time title="{{ item.publication_date | date('j F, Y, H:i') }}">{{ item.publication_date | date('d M Y') }}</time>
                                    </div>
                                {% endif %}
                                {% if blogItems4.showAuthor %}
                                    <div class="blog-item-author">
                                        {{ 'by'|_ }} {{ item.author.first_name }} {{ item.author.last_name }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="blog-item-title">
                            <h3>
                                <a href="{{ postPage | page({ slug: item.slug }) }}" title="{{ item.title }}">
                                    {{ item.title_short }}
                                </a>
                            </h3>
                        </div>
                        <div class="blog-item-excerpt content_section">
                            <p class="mt-0 line-clamp-4">{{ item.excerpt }}</p>
                        </div>
                        <div class="mt-auto"></div>
                    </div>
                    <a href="{{ postPage | page({ slug: item.slug }) }}" title="{{ item.title }}" class="blog-item-overlay-link"></a>
                </div>
            {% endfor %}
        </div>
        <div class="text-center mt-16">
            {% partial 'ui/button' text="Alle berichten" size="base" style="outline" url=blogPage|link %}
        </div>
    </div>
</section>
