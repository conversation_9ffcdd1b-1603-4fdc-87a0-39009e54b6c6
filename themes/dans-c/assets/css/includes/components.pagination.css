.pagination-wrapper { @apply flex mt-12; }
.pagination { @apply isolate inline-flex -space-x-px rounded-md shadow-sm; }
.pagination .page-item .page-link { @apply relative inline-flex items-center px-4 py-2 font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-primary-100 focus:z-20 focus:outline-offset-0; }
.pagination .page-item.active .page-link { @apply z-10 bg-primary-800 hover:bg-primary-700 px-4 py-2 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600; }
.pagination .page-item.first .page-link { @apply rounded-l-xl; }
.pagination .page-item.last .page-link { @apply rounded-r-xl; }
.pagination .page-item.disabled .page-link { @apply cursor-default text-gray-400 hover:bg-transparent; }
