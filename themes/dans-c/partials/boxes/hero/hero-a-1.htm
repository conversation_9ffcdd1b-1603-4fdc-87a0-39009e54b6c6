<div class="relative h-full lg:h-screen" style="background-color: {{ box.background_color }};">
    {% if box.background_img %}
    <div class="absolute inset-0 overflow-hidden">
        <img src="{{ box.background_img | media | resize(1920, auto, { 'extension': 'webp' }) }}" alt="" class="h-full w-full object-cover">
    </div>
    {% if box.overlay == 'overlay' %}
    <div class="absolute inset-0 overflow-hidden">
        <div class="bg-primary-800/30 w-full h-full"></div>
    </div>
    {% endif %}
    {% endif %}

    <div class="relative flex items-center justify-center h-full w-full z-20 mt-32 mb-20 lg:mt-0 lg:mb-0" data-boxes-container data-rel="boxes-wrapper">
        <div class="container">
            <div class="flex flex-col items-center space-y-6 lg:space-y-12">
                {% if box.title %}
                <div class="text-center">
                    <h1 class="text-white">{{ box.title | content }}</h1>
                </div>
                {% endif %}
                <div class="content_section_hero max-w-xl text-center text-white drop-shadow-md">
                    {{ box.content | content }}
                </div>
                {% if box.buttons %}
                <div class="flex flex-wrap items-center justify-center space-y-4 md:space-y-0 md:space-x-12">
                    {% for button in box.buttons %}
                        {% partial "ui/button" item = button %}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="absolute inset-x-0 bottom-0 translate-y-[1px]">
        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
             viewBox="0 0 1920 200" class="fill-current w-full" style="enable-background:new 0 0 1920 200; color: #ffffff;" xml:space="preserve">
            <g>
                <path class="st0" d="M1127,138.4c-257,0-416.8-88.5-722.9-88.5c0,0-299.1,6.9-404.1,150h1920V0C1920,0,1384,138.4,1127,138.4z"/>
            </g>
        </svg>
    </div>
</div>
