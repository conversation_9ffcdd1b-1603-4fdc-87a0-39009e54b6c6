<section id="text-columns" class="" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        {% set itemCount = box.columns|length %}
        {% if itemCount == '1' %}
        {% set gridCols = 'xl:mx-auto xl:w-1/2 2xl:w-2/5' %}
        {% set gridColumn = 'md:w-2/3 md:mx-auto xl:w-full xl:mx-0' %}
        {% elseif itemCount == '2' %}
        {% set gridCols = 'space-y-8 lg:mx-auto lg:max-w-7xl lg:space-y-0 lg:grid lg:grid-cols-2 lg:gap-12 xl:gap-20 2xl:gap-28' %}
        {% set gridColumn = 'md:w-2/3 md:mx-auto lg:w-full lg:mx-0' %}
        {% endif %}

        <div class="{{ gridCols }}">
            {% for item in box.columns %}
            <div class="space-y-4 {{ gridColumn }}">
                {% if item.title %}
                <div class="text-center lg:text-left">
                    <h2 class="text-primary-700 ">{{ item.title | content }}</h2>
                </div>
                {% endif %}
                {% if item.content %}
                <div class="content_section text-gray-500">
                    {{ item.content|content }}
                </div>
                {% endif %}
                {% if item.benefits %}
                <div class="flex flex-col space-y-3">
                    {% for benefit in item.benefits %}
                    <div class="flex items-center">
                        <i class="fa-regular fa-circle-check text-primary-700 pr-4 text-xl"></i>
                        <div class="content_section content_section_bp text-gray-500">{{ benefit.title | content }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                {% if item.btn_text %}
                <div class="flex justify-center lg:justify-start pt-4">
                    {% partial "ui/button" item = item %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
</section>