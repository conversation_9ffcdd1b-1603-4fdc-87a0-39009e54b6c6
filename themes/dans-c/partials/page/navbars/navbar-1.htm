[staticMenu mainMenu]
code = "main-menu"
==
{% set mainmenuItems = mainMenu.resetMenu(this.theme.navbar.site_primary_menu) %}

<header id="navbar-1" class="{{ this.page.layout == 'hero' ? 'absolute inset-x-0 top-0 bg-white' : 'bg-white' }} z-40">
    <div class="container">
        <div class="flex flex-wrap items-center justify-between py-4">
            <div class="">
                {% partial 'ui/logo/logo_primair' %}
            </div>
            <nav class="mainmenu" x-data="{ open: false }">
                {% partial 'page/mainmenus/mainmenu-trigger' %}
                {% partial 'page/mainmenus/mainmenu' mainmenuItems=mainmenuItems %}
                {% partial 'page/mobilemenus/mobilemenu' mainmenuItems=mainmenuItems %}
            </nav>
            <div class="navbar-buttons hidden md:block">
                {% for btn in this.theme.navbar.btn %}
                {% partial 'ui/button' item = btn %}
                {% endfor %}
            </div>
        </div>
    </div>
</header>
