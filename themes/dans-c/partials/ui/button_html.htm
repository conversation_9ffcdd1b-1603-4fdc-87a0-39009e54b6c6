{% if item.btn_size == "lg" or size == "lg" %}
{% set btnSize = "btn-lg" %}
{% elseif item.btn_size == "sm" or size == "sm" %}
{% set btnSize = "btn-sm" %}
{% else %}
{% set btnSize = "btn" %}
{% endif %}

{% if item.btn_style == "rounded" or style == "rounded" %}
{% set btnStyle = "btn-rounded" %}
{% elseif item.btn_style == "pill" or style == "pill" %}
{% set btnStyle = "btn-pill" %}
{% else %}
{% set btnStyle = "btn-sharp" %}
{% endif %}

{% if item.btn_type == "elevated" or type == "elevated" %}
{% set btnType = "btn-elevated" %}
{% elseif item.btn_type == "tonal" or type == "tonal" %}
{% set btnType = "btn-tonal" %}
{% elseif item.btn_type == "outlined" or type == "outlined" %}
{% set btnType = "btn-outlined" %}
{% elseif item.btn_type == "link" or type == "link" %}
{% set btnType = "btn-link" %}
{% elseif item.btn_type == "gradient" or type == "gradient" %}
{% set btnType = "btn-gradient" %}
{% else %}
{% set btnType = "btn-filled" %}
{% endif %}

{% if item.btn_color == "secondary" or color == "secondary" %}
{% set btnColor = "btn-secondary" %}

{% elseif item.btn_color == "grayscale" or color == "grayscale" %}
{% set btnColor = "btn-grayscale" %}
{% elseif item.btn_color == "error" or color == "error" %}
{% set btnColor = "btn-error" %}
{% elseif item.btn_color == "warning" or color == "warning" %}
{% set btnColor = "btn-warning" %}
{% elseif item.btn_color == "info" or color == "info" %}
{% set btnColor = "btn-info" %}
{% else %}
{% set btnColor = "btn-primary" %}
{% endif %}

{% set btnUrl = url %}
{% if item.btn_url %}
{% set btnUrl = item.btn_url|link %}
{% endif %}

{% set btnText = text %}
{% if item.btn_text %}
{% set btnText = item.btn_text %}
{% endif %}

{% set btnIcon = icon %}
{% if item.btn_icon %}
{% set btnIcon = item.btn_icon %}
{% endif %}

{% set btnIconPosition = icon_position %}
{% if item.btn_icon_position %}
{% set btnIconPosition = item.btn_icon_position %}
{% endif %}

{% set btnCss = css_class %}
{% if item.btn_css_class %}
{% set btnCss = item.btn_css_class %}
{% endif %}

{% set btnTargetBlank = target_blank %}
{% if item.btn_target_blank %}
{% set btnTargetBlank = item.btn_target_blank %}
{% endif %}

<button class="{{ btnSize }} {{ btnType }} {{ btnStyle }} {{ btnColor }} {{ btnCss }}"
   {{ openmodal ? '@click="open = true"' }}>
{% if btnIcon and btnIconPosition == 'before' %}<i class="{{ btnIcon }} mr-4"></i>{% endif %}
{{ btnText }}
{% if btnIcon and btnIconPosition  == 'after' %}<i class="{{ btnIcon }} ml-4"></i>{% endif %}
</button>
