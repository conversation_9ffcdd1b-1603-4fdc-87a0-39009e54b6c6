handle: filtered-schedule
name: <PERSON>rooster met filter
section: Dans
icon: /app/assets/boxes/dans-a/

spacing:
    - general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            _section1:
                type: section
                label: Agenda Filter
                comment: Als er geen opties gekozen zijn, zullen alle agenda items getoond worden op de website.
                tab: Algemeen
            instructors:
                label: Instructeurs
                type: checkboxlist
                tab: Algemeen
            groups:
                label: Lesgroepen
                type: checkboxlist
                tab: Algemeen
            locations:
                label: Locaties
                type: checkboxlist
                tab: Algemeen
            background_color:
                type: mixin
                tab: Design
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design
