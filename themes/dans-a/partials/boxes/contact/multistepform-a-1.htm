<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="multistepform-a-1"
    data-category="contact"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="container">
        <div class="relative bg-white max-w-5xl mx-auto md:grid md:grid-cols-4 {{ this.theme.design.border_radius }} border border-gray-100 shadow-xl">
            <div class="hidden md:block relative col-span-1 w-full h-full overflow-hidden">

                {% if this.theme.design.border_radius == 'rounded-2xl' %}
                    {% set imgRadius = 'rounded-l-2xl' %}
                {% elseif this.theme.design.border_radius == 'rounded-lg' %}
                    {% set imgRadius = 'rounded-l-lg' %}
                {% elseif this.theme.design.border_radius == 'rounded' %}
                    {% set imgRadius = 'rounded-l' %}
                {% else %}
                    {% set imgRadius = 'rounded-none' %}
                {% endif %}

                {% partial 'atomic/atoms/media/multistep-image' img=box.img title=box.img_title resize_w='600' class='w-full h-full object-cover' %}

                {% if box.overlay %}
                    {% partial 'atomic/atoms/multistep-overlay' color="bg-primary-800/30" %}
                {% endif %}

            </div>
            <div class="relative md:col-span-3 py-12 px-4 md:px-8 lg:px-16 xl:px-24">

                <div class="prose prose-primary dark:prose-primary_inverted max-w-none pb-6 mx-4">
                    {% partial 'atomic/atoms/headings/header-h3' text=box.title %}
                </div>

                <div id="form_default">
                    {% ajaxPartial 'site/dynamic-multistepform' %}
                </div>

            </div>
        </div>
    </div>

</section>
