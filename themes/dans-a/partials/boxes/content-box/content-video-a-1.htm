<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="content-video-a-1"
    data-category="youtube"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="container space-y-8 lg:space-y-12">
        <div class="prose prose-primary dark:prose-primary_inverted max-w-none space-y-2">
            {% if box.title %}
            <div class="text-center">
                {% partial 'atomic/atoms/headings/header-h2' text=box.title %}
            </div>
            {% endif %}
            {% if box.content %}
            <div class="text-center md:w-2/3 md:mx-auto pt-4">
                {% partial 'atomic/molecules/content-section' content=box.content %}
            </div>
            {% endif %}
        </div>
        <div class="max-w-5xl mx-auto md:w-2/3 lg:w-3/5">
            <div class="video-container {{ this.theme.design.border_radius }}">
                {{ box.embed_code|raw }}
            </div>
        </div>
    </div>
</section>

<style>
    .video-container {
        position: relative;
        width: 100%;
        padding-bottom: 56.25%; /* 16:9 aspect ratio (9 / 16 * 100%) */
        overflow: hidden;
    }
    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
</style>
