<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="hero-a-1"
    data-category="hero"
    class="relative h-full lg:h-screen overflow-hidden {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};">

    {% if box.media_type == 'video' %}
        <div class="absolute inset-0 w-full h-full">
            {% partial 'atomic/atoms/media/video'
                class="object-cover w-full h-full"
                video=box.video
                controls=box.video_controls
                loop=box.video_loop
                autoplay=box.video_autoplay
                muted=box.video_muted %}
        </div>
    {% else %}
        <div class="absolute inset-0 w-full h-full">
            {% partial 'atomic/atoms/media/background_image'
                class="object-cover w-full h-full"
                resize_w="1920"
                bg_img=box.img
                title=box.img_title %}
        </div>
    {% endif %}

    {% if box.overlay %}
        {% partial 'atomic/atoms/bg_overlay' color="bg-primary-800/40" %}
    {% endif %}

    <div
        class="relative flex items-center justify-center h-full w-full z-20 mt-32 mb-20 lg:mt-0 lg:mb-0"
        data-boxes-container
        data-rel="boxes-wrapper">

        <div class="max-w-3xl mx-auto px-8">

            <div class="flex flex-col items-center space-y-6 lg:space-y-12">

                <div class="prose prose-primary dark:prose-primary_inverted dark:text-white max-w-none text-center drop-shadow-md">

                    {% if box.title %}
                        {% partial 'atomic/atoms/headings/header-h1' text=box.title %}
                    {% endif %}

                </div>

                <div class="prose md:prose-lg xl:prose-xl 3xl:prose-2xl prose-primary dark:prose-primary_inverted dark:text-white max-w-none text-center drop-shadow-md">

                    {% if box.content %}
                        {% partial 'atomic/molecules/content-section' content=box.content %}
                    {% endif %}

                </div>

                {% if box.buttons %}
                    {% partial 'atomic/molecules/buttons'
                        class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-12"
                        buttons=box.buttons %}
                {% endif %}

            </div>

        </div>

    </div>

    <div class="absolute inset-x-0 bottom-0 translate-y-[1px]">
        {% partial 'atomic/atoms/theme/wave-bottom' svg_color=box.svg_color %}
    </div>

</section>
