<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="social-feed-a-1"
    data-category="social"
    class="relative overflow-hidden {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="relative container space-y-8 lg:space-y-12 z-10">
        <div class="space-y-2">

            {% partial 'atomic/molecules/content-heading-centered' box=box %}

        </div>

        <div class="relative">
            {% component 'InstagramFeed' backgroundColor=box.background_color maxItems=box.max_items hashtags=box.hashtags ? box.hashtags : [] %}
        </div>
    </div>

    <div class="absolute inset-x-0 bottom-0">
        {% partial 'atomic/atoms/theme/wave-bottom' class="translate-y-[1px]" %}
        <div class="bg-white h-32 lg:h-40 xl:h-32 2xl:h-24"></div>
    </div>
</section>
