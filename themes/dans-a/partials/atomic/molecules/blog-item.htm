<div class="blog-item flex flex-col gap-2 {{ loop.first ? 'md:col-span-2 md:row-span-2' }}">
    <div class="blog-item-thumb relative">
        <div class="absolute p-2 flex flex-wrap gap-2">
            {% for cat in item.categories %}
                <a href="{{ categoryPage | page({ slug: cat.slug }) }}" title="{{ cat.title }}" class="inline-block bg-white text-gray-700 font-bold tracking-wide text-xs rounded shadow-sm leading-none py-1 px-2">{{ cat.title }}</a>
            {% endfor %}
        </div>
        <a href="{{ postPage | page({ slug: item.slug }) }}" title="{{ item.title }}">
            {% if item.thumbnail_image %}
                <img src="{{ item.thumbnail_image | media | resize(600) }}" alt="{{ item.title }}" class="w-full h-full object-cover rounded">
            {% else %}
                <img src="{{ item.image | media | resize(600) }}" alt="{{ item.title }}" class="w-full h-full object-cover rounded">
            {% endif %}
        </a>
    </div>
    <div class="blog-item-info">
        <div class="flex flex-wrap justify-between">
            {% if postList3.showDates %}
                <div>
                    <time title="{{ item.publication_date | date('j F, Y, H:i') }}">{{ item.publication_date | date('d M Y') }}</time>
                </div>
            {% endif %}
            {% if postList3.showAuthor %}
                <div>
                    by {{ item.author.first_name }} {{ item.author.last_name }}
                </div>
            {% endif %}
        </div>
    </div>
    <div class="blog-item-title">
        <h3>
            <a href="{{ postPage | page({ slug: item.slug }) }}" title="{{ item.title }}">
                {{ item.title_short }}
            </a>
        </h3>
    </div>
    <div class="blog-item-button mt-auto">
        {% set buttonLeesVerder = "Lees verder"|_  %}
        {% partial 'ui/button' text=buttonLeesVerder size="sm" style="outline" url=postPage | page({ slug: item.slug }) %}
    </div>
</div>
