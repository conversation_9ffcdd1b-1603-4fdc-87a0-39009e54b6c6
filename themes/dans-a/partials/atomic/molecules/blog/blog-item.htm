[postList]
==

<div class="group/card flex flex-col gap-2 overflow-hidden h-full">

    <div class="relative">

        <div class="absolute p-2 flex flex-wrap gap-2 z-20">
            {% for cat in item.categories %}
                {% partial 'atomic/atoms/batch-link-small' link=categoryPage | page({ slug: cat.slug }) label=cat.title %}
            {% endfor %}
        </div>

        {% if item.thumbnail_image %}
            {% set itemImage = item.thumbnail_image %}
            {% set imgDescription = item.thumb_img_title %}
        {% else %}
            {% set itemImage = item.image %}
            {% set imgDescription = item.img_title %}
        {% endif %}

        <div class="relative aspect-thumb overflow-hidden shrink-0">
            {% partial 'atomic/atoms/media/image' img=itemImage title=imgDescription resize_w='500' class='w-full h-full object-cover group-hover/card:scale-110 transition-all duration-500' %}
            <a href="{{ postPage | page({ slug: item.slug }) }}" title="{{ item.title }}" class="absolute inset-0"></a>
        </div>

    </div>

    <div class="flex flex-col h-full flex-1 gap-y-4">

        <div class="space-y-1">
            <div class="text-gray-400 text-xs">
                <div class="flex flex-wrap justify-between w-full gap-1">
                    {% if postList.showAuthor %}
                        {% partial 'atomic/atoms/blog/author' class='text-xs' firstName=item.author.first_name lastName=item.author.last_name %}
                    {% endif %}
                    {% if postList.showDates %}
                        {% partial 'atomic/atoms/blog/date' date=item.publication_date class='ml-auto text-xs' %}
                    {% endif %}
                </div>
            </div>

            <div class="prose prose-primary dark:prose-primary_inverted max-w-none">

                {% if item.title_short %}
                    {% set blogTitle = item.title_short %}
                {% else %}
                    {% set blogTitle = item.title %}
                {% endif %}

                {% partial 'atomic/atoms/cards/card-heading' text=blogTitle class='text-lg' %}
            </div>
        </div>

        <div class="">
            {% if item.excerpt %}
                {% set blogContent = item.excerpt %}
            {% else %}
                {% set blogContent = item.content %}
            {% endif %}

            {% partial 'atomic/atoms/blog/blog-content-small' text=blogContent truncate='140' %}
        </div>

        <div class="mt-auto">

            {% if box.is_dark_bg or dark  %}
                {% set btnType = 'filled' %}
            {% else %}
                {% set btnType = 'outlined' %}
            {% endif %}

            {% set btnText = "Lees meer"|_ %}
            {% partial 'ui/button'
                text=btnText
                color="primary"
                type=btnType
                style="rounded"
                size="sm"
                url=postPage | page({ slug:item.slug })
                css_class="flex w-full items-center justify-center" %}
        </div>

    </div>

</div>
