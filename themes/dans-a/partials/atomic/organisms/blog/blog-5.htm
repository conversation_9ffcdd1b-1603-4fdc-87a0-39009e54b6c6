<div class="space-y-8 xl:space-y-0 xl:grid xl:grid-cols-12 xl:grid-rows-2 xl:gap-8">
    {% for item in posts %}
        {% if loop.index == 1 %}
            <div class="md:hidden">
                {% partial 'atomic/molecules/blog/blog-item-card' item=item categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
            </div>
            <div class="hidden md:block xl:col-start-1 xl:row-start-1 xl:col-span-7 xl:row-span-1">
                {% partial 'atomic/molecules/blog/blog-item-card-wide' item=item categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
            </div>
        {% elseif loop.index == 2 %}
            <div class="md:hidden xl:block xl:col-start-8 xl:row-start-1 xl:col-span-5 xl:row-span-2">
                {% partial 'atomic/molecules/blog/blog-item-card' item=item categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
            </div>
            <div class="hidden md:block xl:hidden">
                {% partial 'atomic/molecules/blog/blog-item-card-wide' item=item categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
            </div>
        {% elseif loop.index == 3 %}
            <div class="md:hidden">
                {% partial 'atomic/molecules/blog/blog-item-card' item=item categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
            </div>
            <div class="hidden md:block xl:col-start-1 xl:row-start-2 xl:col-span-7 xl:row-span-1">
                {% partial 'atomic/molecules/blog/blog-item-card-wide' item=item categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
            </div>
        {% endif %}
    {% endfor %}
</div>
