<div class="space-y-8 md:space-y-0 md:grid lg:grid-flow-row-dense md:grid-cols-2 lg:grid-cols-4 gap-4 p-4">
    <div class="md:col-span-2 lg:row-span-2">
        <div class="lg:aspect-square bg-gray-100 border p-4 md:p-8 lg:p-10 flex items-center rounded-md">
            <div class="space-y-8">
                <div>
                    {% partial 'atomic/molecules/prose/prose-primary' body %}
                        {% partial 'atomic/molecules/content-heading' %}
                        {% partial 'atomic/molecules/content-section' content=box.content %}
                    {% endpartial %}
                </div>
                {% if box.buttons %}
                    {% partial 'atomic/molecules/buttons' buttons=box.buttons class="buttons-wrap" %}
                {% endif %}
            </div>

        </div>
    </div>

    {% for item in projects %}
        <div class="{{ loop.index == '5' ? 'lg:col-span-3' }}">
            <div class="pf-project {{ card_size == "sm" ? 'pf-project-sm' }} group transition-all duration-300 {{ project_class }}">
                <a href="{{ projectPage | page({ slug: item.slug }) }}" title="{{ 'Bekijk project'|_ }}" class="{{ loop.index == '5' ? 'aspect-thumb lg:aspect-auto lg:h-[228px] xl:h-[292px]' : 'aspect-thumb lg:aspect-square' }} block h-full">
                    <div class="absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-gray-800 z-20"></div>

                    <img src="{{ item.overview_image | media | resize(800, 600, { mode: 'crop' }) }}" alt="{{ item.title }}" class="lazy w-full h-full object-cover group-hover:scale-110 z-10">

                    <div class="pf-project-title z-30">{{ item.title }}</div>
                </a>
            </div>
        </div>
    {% endfor %}
</div>
