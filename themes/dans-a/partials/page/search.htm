<div x-data="searchbox" x-cloak>
  <a href="#" class="inline-flex md:text-lg font-medium transition mr-6 text-gray-500 hover:text-gray-800" @click="openSearch"><i class="fas fa-search"></i></a>

  <!-- Search modal -->
<div
  class="relative z-20"
  role="dialog"
  aria-modal="true"
  x-show="search"
  x-on:keydown.escape.prevent.stop="closeSearch"
>

  <!-- Backdrop -->
  <div
    class="fixed inset-0 bg-gray-500 backdrop-blur-sm bg-opacity-50 transition-opacity"
    :aria-hidden="!search"
    x-show="search"
    x-transition:enter="ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
  ></div>

  <!-- Panel-->
  <div class="fixed inset-0 z-10 overflow-y-auto p-4 sm:p-6 md:p-20" x-on:keydown.escape.prevent.stop="closeSearch">
    <div
      class="mx-auto max-w-xl transform divide-y divide-gray-100 overflow-hidden rounded-xl bg-white shadow-2xl ring-1 ring-black ring-opacity-5 transition-all"
      aria-hidden="true"
      x-show="search"
      x-transition:enter="ease-out duration-300"
      x-transition:enter-start="opacity-0 scale-95"
      x-transition:enter-end="opacity-100 scale-100"
      x-transition:leave="ease-in duration-200"
      x-transition:leave-start="opacity-100 scale-100"
      x-transition:leave-end="opacity-0 scale-95"
      x-on:click.away="closeSearch"
      x-on:click.stop
      x-trap.noscroll.inert="search"
    >
      <div
        class="relative"
      >
        <!-- Heroicon name: solid/search -->
        <svg class="pointer-events-none absolute top-3.5 left-4 h-5 w-5 lg:w-6 lg:h-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
        </svg>
        <input type="text" class="h-12 lg:h-14 w-full border-0 bg-transparent pl-11 lg:pl-14 pr-4 text-gray-800 placeholder-gray-400 focus:ring-0 sm:text-sm lg:text-xl" placeholder="Search..." role="combobox" aria-expanded="false" aria-controls="options" id="searchInput" x-model="searchInput">
      </div>

      <!-- Results, show/hide based on command palette state -->
      <ul class="max-h-96 scroll-py-3 overflow-y-auto p-3" id="options" role="listbox" x-show="results.length > 0">
        <!-- Active: "bg-gray-100" -->
        <li class="group flex cursor-default select-none rounded-xl p-3" x-id="['option-title']" role="option" tabindex="-1">
          <div class="flex h-10 w-10 flex-none items-center justify-center rounded-lg bg-primary-500">
            <!-- Heroicon name: outline/pencil-alt -->
            <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <div class="ml-4 flex-auto">
            <!-- Active: "text-gray-900", Not Active: "text-gray-700" -->
            <p class="text-sm font-medium text-gray-700">{{ 'Text'|_ }}</p>
            <!-- Active: "text-gray-700", Not Active: "text-gray-500" -->
            <p class="text-sm text-gray-500">{{ 'Add freeform text with basic formatting options.'|_ }}</p>
          </div>
        </li>

        <!-- More items... -->
      </ul>

      <!-- Empty state, show/hide based on command palette state -->
      <div class="py-14 px-6 text-center text-lg sm:px-14" x-show="searchInput && results.length === 0">
        <!-- Heroicon name: outline/exclamation-circle -->
        <svg class="mx-auto h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p class="mt-4 font-semibold text-gray-900">{{ 'Geen resultaten gevonden'|_ }}</p>
        <p class="mt-2 text-gray-500">{{ 'Er zijn geen pagina's gevonden met deze zoekterm. Probeer het opnieuw.'|_ }}</p>
      </div>
    </div>
  </div>
</div>
</div>