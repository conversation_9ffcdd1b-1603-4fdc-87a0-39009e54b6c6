{% if __SELF__.form %}
    <div class="form-alert-{{ __SELF__.form.id }}"></div>

    {% if __SELF__.form.description %}
        <p class="mt-2 mb-8 text-lg leading-8 text-gray-600">{{ __SELF__.form.description }}</p>
    {% endif %}

    {{ form_open({
            'id': __SELF__.form.code,
            'class': __SELF__.form.css_class,
            'data-request': __SELF__ ~ '::onSubmit',
            'data-request-data': { form: __SELF__.form.code },
            'data-request-success': 'resetForm($(this))',
            'data-request-validate': true,
            'novalidate': true,
        })
    }}
        {% component 'spamProtection' %}

        <div class="form-wrapper">
            {{ __SELF__.markup|raw }}
        </div>

    {{ form_close() }}
{% else %}
    <div class="alert alert-warning" role="alert">
        Form with code "{{ formCode }}" does not exist! Did you select correct form in component settings?
    </div>
{% endif %}
