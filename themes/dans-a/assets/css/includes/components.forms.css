/* Form fields wrapper */
.form-wrapper { @apply flex flex-wrap -mx-4; }

/* Field wrapper */
.form-field-wrapper {
    @apply mb-6 relative w-full px-4;
}
.form-field-wrapper .form-group {
    @apply mt-2;
}

/* Label */
.form-field-wrapper label.form-label {
    @apply block font-medium leading-6 text-gray-900;
}
.form-field-disabled label.field-label {
    @apply text-gray-400;
}
.form-field-wrapper label.checkbox-list-label {
    @apply text-base font-semibold leading-6 text-gray-900 mb-2;
}

/* Comment */
.form-field-wrapper .field-comment {
    @apply mt-2 text-sm text-gray-500;
}

/* Input, Textarea */
.form-field-wrapper .form-field {
    @apply relative rounded-md shadow-sm;
}
.form-field-wrapper .form-control {
    @apply block w-full rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200;
}

/* Select/Dropdown */
.form-field-wrapper .form-select {
    @apply mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-primary-600 sm:text-sm sm:leading-6 placeholder:text-gray-400 disabled:bg-gray-100 disabled:cursor-not-allowed disabled:border-gray-300/70;
}


/* Checkbox & Radio wrap */
.form-field-wrapper .form-check-wrap, .form-field-wrapper .form-radio-wrap {
    @apply relative flex items-start;
}
.form-field-wrapper .form-check, .form-field-wrapper .form-radio {
    @apply flex h-6 items-center;
}

/* Checkbox & Radio label */
.form-check-label-wrap {
    @apply ml-3 text-sm leading-6;
}
.form-checkbox-list-wrap {
    @apply mb-2;
}
.form-field-wrapper .form-check-label,
.form-field-wrapper .field-radio-label {
    @apply select-none font-medium text-gray-900 text-base;
}
.form-check-label-wrap .form-text {
    @apply text-gray-500;
}
.form-check-label-wrap .form-text a {
    @apply text-primary-500 font-medium hover:text-primary-600 underline;
}

.form-field-disabled .form-check-label, .form-field-disabled .form-radio-label {
    @apply cursor-not-allowed text-gray-400;
}

/* Checkbox & Radio input */
.form-check-input, .form-radio-input {
    @apply h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-600 disabled:bg-gray-100 disabled:cursor-not-allowed disabled:border-gray-400;
}

/* Radio input */
.form-field-wrapper input[type="radio"] {
    @apply rounded-full;
}

/* Colorpicker */
.form-field-color { @apply flex relative; }
.form-field-color .form-field-color-box { @apply w-10 block h-full relative; }
.form-field-color .form-field-color-box .colorpicker-bg { @apply absolute inset-0 pointer-events-none;  }
.form-field-color input[type="color"] { @apply w-10 block absolute inset-0 h-8 appearance-none border-0; }
.form-field-color .form-control { @apply rounded-l-none w-auto; }

/* Submit button */
.form-submit-button {
    @apply rounded-md bg-primary-500 py-3 px-6 font-semibold text-white shadow-sm hover:bg-primary-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500;
}

/* Validation */
.form-field-wrapper .form-control.is-invalid {
    @apply block w-full rounded-md border-0 pr-10 text-red-900 ring-1 ring-inset ring-red-300 placeholder:text-red-300 focus:ring-2 focus:ring-inset focus:ring-red-500 sm:text-sm sm:leading-6;
}
.form-field-wrapper .invalid-icon { @apply hidden; }
.form-field-wrapper .form-control.is-invalid ~ .invalid-icon {
    @apply pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3;
}
.form-field-wrapper .invalid-icon-svg {
    @apply h-5 w-5 text-red-500;
}
.form-field-wrapper .invalid-feedback {
    @apply mt-1 text-sm text-red-600;
}
.form-check-input.is-invalid { @apply border-red-600 checked:border-primary-500; }

.form-field.has-error textarea {
    @apply border-red-500;
}

.form-field-error-message {
    @apply mt-2 text-sm text-red-600 font-medium;
}

/* Horizontal form */
.form-horizontal .form-field-wrapper {
    @apply grid grid-cols-4 gap-6;
}

.form-horizontal .form-field-wrapper .form-label {
    @apply col-span-1 pt-1.5;
}

.form-horizontal .form-field-wrapper .form-group {
    @apply col-span-3 mt-0;
}

.form-field-wrapper.row-1-2,
.form-field-wrapper.row-2-4 { @apply md:w-1/2; }
.form-field-wrapper.row-1-3 { @apply md:w-1/3; }
.form-field-wrapper.row-2-3 { @apply md:w-2/3; }
.form-field-wrapper.row-1-4 { @apply md:w-1/4; }
.form-field-wrapper.row-3-4 { @apply md:w-3/4; }


/* New */



/* Form fields wrapper */
.form-wrapper { @apply flex flex-wrap -mx-4; }
.form-wrapper > div { @apply w-full px-4 mb-4; }
.form-fieldset-flex > div { @apply w-full px-4 mb-4; }
.form-fieldset-buttons { @apply px-4 mb-4; }
.form-wrapper > .col-md-12 { @apply md:w-full; }
.form-wrapper > .col-md-11 { @apply md:w-11/12; }
.form-wrapper > .col-md-10 { @apply md:w-10/12; }
.form-wrapper > .col-md-9 { @apply md:w-9/12; }
.form-wrapper > .col-md-8 { @apply md:w-8/12; }
.form-wrapper > .col-md-7 { @apply md:w-7/12; }
.form-wrapper > .col-md-6 { @apply md:w-6/12; }
.form-wrapper > .col-md-5 { @apply md:w-5/12; }
.form-wrapper > .col-md-4 { @apply md:w-4/12; }
.form-wrapper > .col-md-3 { @apply md:w-3/12; }
.form-wrapper > .col-md-2 { @apply md:w-2/12; }
.form-wrapper > .col-md-1 { @apply md:w-1/12; }

/* Field wrapper */
.form-field-wrapper {
    @apply mb-6 relative w-full px-4;
}
.form-field-wrapper.row-1-2,
.form-field-wrapper.row-2-4 { @apply md:w-1/2; }
.form-field-wrapper.row-1-3 { @apply md:w-1/3; }
.form-field-wrapper.row-2-3 { @apply md:w-2/3; }
.form-field-wrapper.row-1-4 { @apply md:w-1/4; }
.form-field-wrapper.row-3-4 { @apply md:w-3/4; }


/* Form Label */
.form-wrapper .form-label { @apply mb-2 inline-block text-gray-500 font-bold; }
.dark .form-wrapper .form-label { @apply text-white font-semibold; }
.form-wrapper .form-text { @apply text-sm text-gray-400 mt-1; }

/* Form Input & Select */
.form-wrapper .form-control,
.form-wrapper .form-select { @apply block w-full px-3 py-2.5 border border-gray-300 rounded-md ring-primary-300 focus:ring-2 focus:border-primary-300 bg-white; }
.dark .form-wrapper .form-control,
.dark .form-wrapper .form-select { @apply block w-full px-3 py-2.5 border border-gray-300 rounded-md ring-primary-600 focus:ring-2 focus:border-primary-600 bg-white; }

/* Form Checkbox */
.form-wrapper .form-check { @apply flex flex-wrap items-center gap-x-3; }
.form-wrapper .form-check .form-check-input { @apply rounded border-gray-300 text-primary-500 focus:ring-primary-300 ring-offset-1 focus:border-primary-300 checked:border-primary-500; }
.form-wrapper .form-check input[type="radio"].form-check-input { @apply rounded-full; }

/* Form Upload */
.form-wrapper .form-control::file-selector-button { @apply py-1.5 px-3 -my-2.5 -ml-3 border-0 border-r border-solid border-gray-300 appearance-none shadow-none mr-2 transition bg-gray-200; }
.form-wrapper .form-control:hover::file-selector-button { @apply bg-gray-300; }


/* Submit */
.form-wrapper .btn-primary { @apply bg-primary-500 hover:bg-primary-600 rounded-full text-white;}

/* Validation */
.form-wrapper .form-control.is-invalid { @apply border-red-500 focus:ring-red-300 pr-6 bg-no-repeat; }
.form-wrapper .form-control.is-invalid {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.form-wrapper textarea.form-control.is-invalid {
    background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.form-wrapper .invalid-feedback { @apply text-sm font-medium text-red-500 mt-1; }
.form-wrapper .form-check .invalid-feedback { @apply w-full; }

/* Floating Labels */

.form-floating { @apply relative; }

.form-floating > input.form-control,
.form-floating > textarea.form-control { @apply py-4 px-3 transition-all; }

.form-floating > label { @apply absolute left-3 top-4 select-none transition-all; }

.form-floating > input.form-control:focus,
.form-floating > textarea.form-control:focus,
.form-floating > input.form-control:not(:placeholder-shown),
.form-floating > textarea.form-control:not(:placeholder-shown),
.form-floating > .form-select { @apply pt-6 pb-2; }

.form-floating > input.form-control:focus ~ label,
.form-floating > textarea.form-control:focus ~ label,
.form-floating > input.form-control:not(:placeholder-shown) ~ label,
.form-floating > textarea.form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label { @apply text-gray-500 text-xs top-2.5; }

.form-horizontal .form-wrapper > div,
.form-horizontal .form-wrapper .form-button-wrap {
    @apply md:flex md:flex-wrap;
}
.form-horizontal .form-wrapper .form-label,
.form-horizontal .form-wrapper .form-button-wrap .form-empty-space { @apply w-full md:w-3/12 md:text-right md:pr-4 relative top-2; }
.form-horizontal .form-wrapper .form-control,
.form-horizontal .form-wrapper .form-select,
.form-horizontal .form-wrapper .form-button-wrap .form-button { @apply w-full md:w-9/12; }
.form-horizontal .form-wrapper .invalid-feedback,
.form-horizontal .form-wrapper .form-text { @apply md:w-full md:text-right; }

.multistepform-wrapper .multistepform-submit { @apply flex flex-wrap gap-x-8 gap-y-6 justify-between; }
.multistepform-wrapper .multistepform-submit .prev-step { @apply px-0; }
