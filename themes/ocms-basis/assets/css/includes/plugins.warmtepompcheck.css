/* Wrapper */
.warmtepompcheck { @apply md:min-h-[600px] flex items-center; }

/* Fieldset heading */
.warmtepompcheck .fs-heading { @apply text-center mb-12; }
.warmtepompcheck .fs-heading .fs-heading-step { @apply ml-4 flex-shrink-0 order-1; }
.warmtepompcheck .fs-heading .fs-heading-step-icon { @apply flex h-6 md:h-10 w-6 md:w-10 items-center justify-center rounded bg-primary text-white sm:shrink-0; }
.warmtepompcheck .fs-heading .fs-heading-step-text { @apply text-lg md:text-xl font-bold; }

.warmtepompcheck .fs-heading .fs-heading-title { @apply w-full md:w-auto ml-4 mt-4 md:mt-0 order-3 md:order-2; }
.warmtepompcheck .fs-heading .fs-heading-title-text { @apply text-lg font-medium leading-none text-gray-900 mb-0; }
/* .warmtepompcheck .fs-heading .fs-heading-title-text { @apply text-lg font-medium leading-none text-white mb-0; } */

.warmtepompcheck .fs-heading .fs-heading-check { @apply ml-auto order-2 md:order-3 h-6 md:h-auto text-green-600 text-2xl; }
/* .warmtepompcheck .fs-heading .fs-heading-check { @apply ml-auto order-2 md:order-3 h-6 md:h-auto text-white text-2xl; } */

/* Fieldset body */
.warmtepompcheck .fs-body { @apply px-4 sm:px-6 lg:px-8; }

/* Answer grid */
.warmtepompcheck .fs-body .fs-field-grid { @apply grid grid-cols-1 md:grid-cols-2 gap-3; }
.warmtepompcheck .fs-body .fs-field-grid-md { @apply grid grid-cols-1 md:grid-cols-2 gap-3; }
.warmtepompcheck .fs-body .fs-field-grid-lg { @apply grid grid-cols-1 md:grid-cols-2 gap-3; }

/* Field Label */
.warmtepompcheck .fs-body .field-label { @apply text-lg font-medium leading-6 text-gray-900; }
.warmtepompcheck .fs-body .field-comment { @apply text-gray-600; }

/* Field: Radio */
.warmtepompcheck .field-radio { @apply border rounded-md py-3 px-3 flex items-center justify-center text-center text-sm font-medium sm:flex-1 cursor-pointer focus:outline-none bg-white border-gray-200 text-gray-900 hover:bg-gray-50 relative transition-all; }
.warmtepompcheck .field-radio.selected { @apply ring-1 ring-primary border-primary; }

/* Field: Checkbox */
.warmtepompcheck .field-checkbox { @apply block; }
.warmtepompcheck .field-checkbox-label { @apply border rounded-md py-3 px-3 flex items-center justify-center text-sm font-medium sm:flex-1 cursor-pointer focus:outline-none bg-white border-gray-200 text-gray-900 hover:bg-gray-50 relative transition-all; }
.warmtepompcheck .field-checkbox input:checked ~ .field-checkbox-label { @apply ring-1 ring-primary border-primary; }
.warmtepompcheck .field-checkbox-simple { @apply h-4 w-4 rounded border-gray-300 text-primary-darker focus:ring-primary; }
.warmtepompcheck .field-checkbox-simple-label { @apply font-medium text-gray-700; }

.warmtepompcheck .field-radio .input-check,
.warmtepompcheck .field-checkbox-label .input-check { @apply absolute top-1 right-1 text-gray-100 transition-all; }
.warmtepompcheck .field-radio:hover .input-check,
.warmtepompcheck .field-checkbox-label:hover .input-check { @apply text-gray-200; }
.warmtepompcheck .field-radio.selected .input-check,
.warmtepompcheck .field-checkbox input:checked ~ .field-checkbox-label .input-check { @apply text-primary; }

/* Field: Text */
.warmtepompcheck .field-text-label { @apply block text-sm font-medium text-gray-900; }
.warmtepompcheck .field-text-wrap { @apply relative mt-1 rounded-md shadow-sm; }
.warmtepompcheck .field-text-leading { @apply pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3; }
.warmtepompcheck .field-text-trailing { @apply pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3; }
.warmtepompcheck .field-text-leading-text,
.warmtepompcheck .field-text-trailing-text { @apply text-gray-500 sm:text-sm; }
.warmtepompcheck .field-text-input { @apply block w-full rounded-md border-gray-300 focus:border-primary focus:ring-primary sm:text-sm; }
.warmtepompcheck .field-text-input.has_leading { @apply pl-7; }
.warmtepompcheck .field-text-input.has_trailing { @apply pr-12; }

/* Buttons */
.warmtepompcheck .fs-buttons { @apply flex flex-wrap flex-row-reverse justify-center mt-12; }

/* Next button */
.warmtepompcheck .fs-buttons .fs-button-next { @apply inline-flex items-center rounded-full border border-transparent bg-primary px-8 py-2 text-base font-medium text-white shadow-sm hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary-lighter focus:ring-offset-2 disabled:bg-gray-400; }

/* Prev button */
.warmtepompcheck .fs-buttons .fs-button-prev { @apply inline-flex items-center rounded-full px-8 py-2 text-base font-medium text-gray-700; }

/* Submit button */
.warmtepompcheck .fs-buttons .fs-button-submit { @apply inline-flex items-center rounded-full border border-transparent bg-orange-600 px-8 py-2 text-base font-medium text-white shadow-sm hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:bg-gray-400; }

/* Fieldset steps */
.warmtepompcheck .fs-steps-list { @apply flex items-center justify-center space-x-2 mb-6; }
.warmtepompcheck .fs-steps-list .fs-step { @apply h-2 w-8 rounded-full; }

/* Step: Current */
.warmtepompcheck .fs-steps-list .fs-step-current { @apply bg-primary; }

/* Step: Completed */
.warmtepompcheck .fs-steps-list .fs-step-completed { @apply bg-primary; }

/* Step: Upcoming */
.warmtepompcheck .fs-steps-list .fs-step-upcoming { @apply bg-gray-200; }
