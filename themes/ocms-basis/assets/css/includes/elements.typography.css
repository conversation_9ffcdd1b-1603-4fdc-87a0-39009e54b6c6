h1, .h1 { @apply text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-700 leading-snug md:leading-snug lg:leading-snug xl:leading-snug; }
h2, .h2 { @apply text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight text-gray-600 leading-snug md:leading-snug lg:leading-snug xl:leading-snug; }
h3, .h3 { @apply text-xl lg:text-2xl font-bold text-gray-600 leading-snug md:leading-snug lg:leading-snug xl:leading-snug; }
h4, .h4 { @apply text-lg font-medium text-gray-800; }
h5, .h5 { @apply font-medium text-gray-600 dark:text-gray-300 mb-1; }

h1 strong,
h2 strong,
h3 strong { @apply font-bold text-primary-darker; }

h1.lead { @apply font-light text-gray-700 text-4xl sm:text-5xl lg:text-6xl 2xl:text-7xl; }

a { @apply transition duration-150 ease-in-out; }

.content_section h1 { @apply mb-4; }
.content_section h2 { @apply mb-3; }
.content_section h3 { @apply mb-2; }
.content_section h1 a,
.content_section h2 a,
.content_section h3 a { @apply text-primary hover:text-primary-hover hover:underline; }
.content_section h1 b,
.content_section h1 strong,
.content_section h2 b,
.content_section h2 strong,
.content_section h3 b,
.content_section h3 strong { @apply text-primary font-bold; }
.content_section h4 { @apply text-lg md:text-xl font-semibold leading-7 text-primary-darker mb-2; }
.content_section h1.success_heading,
.content_section h2.success_heading,
.content_section h3.success_heading,
.content_section h4.success_heading { @apply text-green-600; }
.content_section p { @apply leading-7 text-gray-600 mb-6; }
.content_section p a:not(.btn):not(.btn-lg) { @apply text-primary font-semibold hover:text-primary-hover hover:underline; }
.content_section img { @apply max-w-full; }
.content_section ul, .content_section ol { @apply pl-4 space-y-2 mb-4; }
.content_section ul { @apply list-disc marker:text-secondary; }
.content_section ol { @apply list-decimal; }
.content_section li { @apply text-base leading-7 text-gray-600; }
.content_section table { @apply w-full align-top; }
.content_section table tr { @apply align-top; }
.content_section table td, .content_section table th { @apply align-top; }
.content_section blockquote { @apply bg-gray-100 border-l-4 border-gray-300 text-xl italic font-medium leading-relaxed p-4 my-4; }
.content_section blockquote p { @apply m-0 before:content-['“'] after:content-['„']; }
.content_section blockquote blockquote { @apply bg-gray-200 border-gray-400; }

.content_section_sm p { @apply text-sm leading-6 mb-4; }
.content_section_sm li { @apply text-sm leading-5; }
.content_section_sm h2 { @apply text-2xl md:text-3xl lg:text-3xl font-bold tracking-tight text-gray-600; }

.content_section_faded p,
.light .content_section_faded p { @apply text-gray-500; }

.dark .content_section h2,
.dark .content_section h3 { @apply text-white; }
.dark .content_section p { @apply text-gray-50; }
.dark .content_section h4 { @apply text-primary-lighter; }
.dark .content_section blockquote { @apply bg-gray-700 border-primary; }
.dark .content_section blockquote blockquote { @apply bg-gray-600 border-primary-lighter; }

.dark .content_section_faded p { @apply text-gray-700; }

.light .content_section h2 { @apply text-gray-600; }
.light .content_section h3 { @apply text-gray-500; }
.light .content_section p { @apply text-gray-600; }
.light .content_section h4 { @apply text-primary-darker; }
.light .content_section blockquote { @apply bg-gray-100 border-gray-300; }
.light .content_section blockquote blockquote { @apply bg-gray-200 border-gray-400; }

.content_section.p-lg p { @apply text-lg; }
.content_section.p-xl p { @apply text-xl; }

div:has(.prose) { @apply
    prose-headings:leading-snug
    prose-h1:text-4xl
    prose-headings:text-balance
    md:prose-h1:text-5xl
    lg:prose-h1:text-6xl
    prose-h2:text-2xl
    md:prose-h2:text-3xl
    lg:prose-h2:text-4xl
    prose-h3:text-xl
    md:prose-h3:text-2xl
    prose-h4:text-lg
    md:prose-h4:text-lg
    prose-h4:font-semibold
    prose-lead:font-light
    prose-img:my-0;
}

.sidebar-item div:has(.prose) { @apply
    prose-h3:text-lg
    md:prose-h3:text-xl
}

.secondary-title-color div:has(.prose) { @apply prose-strong:text-secondary prose-h4:text-secondary dark:prose-strong:text-secondary dark:prose-h4:text-secondary; }

.fr-img-caption .fr-inner { @apply text-xs text-gray-500; }
.fr-img-caption .fr-img-wrap img { @apply mb-0; }
img.fr-dii.fr-fil,
span.fr-img-caption.fr-dii.fr-fil { @apply mr-4; }
img.fr-dii.fr-fir,
span.fr-img-caption.fr-dii.fr-fir { @apply ml-4; }
img.fr-dib,
span.fr-img-caption.fr-dib { @apply my-6; }
