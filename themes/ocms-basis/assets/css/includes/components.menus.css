/* Navbar menu's */
.mainmenu { @apply relative z-40; }
.mainmenu > ul { @apply md:gap-x-8 lg:gap-x-12; }
.mainmenu > ul > li { @apply relative; }
.mainmenu > ul > li > p { @apply flex py-6 text-gray-600 hover:text-gray-800 leading-none; }
.mainmenu > ul > li > a { @apply flex py-6 text-gray-600 hover:text-gray-800 leading-none; }
.mainmenu > ul > li.active > a { @apply text-gray-900 hover:text-gray-900 font-medium; }

.mainmenu > ul > li > ul { @apply hidden absolute top-full bg-white -translate-x-4 rounded-b w-64 shadow overflow-hidden; }
.mainmenu > ul > li:hover > ul { @apply block divide-y divide-gray-100 z-50; }
.mainmenu > ul > li > ul > li > a { @apply block py-2 px-4 text-sm text-gray-500 hover:text-gray-700; }

.navbar-bottom .mainmenu ul li a { @apply text-gray-500 hover:text-gray-700; }

/* Footer menu's */
.footermenu > ul { @apply md:flex flex-wrap gap-x-8 lg:gap-x-12 gap-y-2 md:justify-center xl:justify-end space-y-4 md:space-y-0; }
.footermenu > ul > li > a { @apply text-center md:text-left block py-2 text-gray-600 hover:text-gray-800 leading-none; }

/* Mobile menu's */
.mobilemenu-wrapper { @apply fixed inset-0 bg-gray-700/40; }
.mobilemenu-inner { @apply absolute right-0 inset-y-0 w-4/5 md:w-1/3 bg-white shadow p-4; }
.mobilemenu-wrapper .close-button { @apply text-gray-500 inline-flex justify-center items-center; }

.mobilemenu-content { @apply pt-6 flow-root; }
.mobilemenu-content .mobilemenu { @apply -my-6 divide-y divide-gray-500/10; }
.mobilemenu > ul { @apply space-y-2 py-6 px-2; }
.mobilemenu > ul > li { @apply -mx-3 block; }
.mobilemenu > ul > li.hasSubmenu > .mobilemenu-item { @apply flex justify-between gap-x-3; }
.mobilemenu > ul > li > a,
.mobilemenu > ul > li > .mobilemenu-item > a { @apply block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900; }
.mobilemenu > ul > li.active > a { @apply bg-gray-100; }

.mobilemenu > ul > li > ul.submenu { @apply space-y-3 pt-1 pb-3 px-3; }

.mobilemenu-contact { @apply mt-auto -mx-4 px-4 py-6 text-gray-500 leading-7 space-y-6 bg-gray-100; }
.mobilemenu-contact .navbar-logo img { @apply max-h-10; }
.mobilemenu-contact a { @apply text-gray-700 font-medium inline-flex items-center text-sm; }

/* Atomic */
#main-nav > ul { @apply flex gap-x-2 lg:gap-x-4 xl:gap-x-6 2xl:gap-x-12; }
#main-nav > ul > li { @apply self-center; }
#main-nav > ul > li > .nav-item { @apply flex items-center py-3 space-x-2 font-medium text-gray-600 hover:text-gray-800 group-hover/menuitem:text-gray-800 dark:text-gray-300 dark:hover:text-gray-100 dark:group-hover/menuitem:text-gray-100 leading-none; }
#main-nav > ul > li > span.nav-item { @apply cursor-default; }
#main-nav > ul > li > .nav-item > .parent-icon { @apply text-sm leading-none; }
#main-nav > ul > li.active > .nav-item { @apply text-primary-600 font-semibold hover:text-primary-700 dark:text-white dark:hover:text-gray-100; }

#top-nav > ul { @apply flex gap-x-6; }
#top-nav > ul > li { @apply self-center; }
#top-nav > ul > li > .nav-item { @apply flex items-center py-1 space-x-2 font-medium text-gray-600 hover:text-gray-800 group-hover/menuitem:text-gray-800 dark:text-gray-300 dark:hover:text-gray-100 dark:group-hover/menuitem:text-gray-100 leading-none; }
#top-nav > ul > li > span.nav-item { @apply cursor-default; }
#top-nav > ul > li > .nav-item > .parent-icon { @apply hidden; }
#top-nav > ul > li.active > .nav-item { @apply text-primary-600 font-semibold hover:text-primary-700 dark:text-white dark:hover:text-gray-100; }

#mobile-nav ul { @apply space-y-2; }
#mobile-nav > ul > li { @apply flex flex-wrap w-full items-center; }
#mobile-nav > ul > li > .nav-item { @apply flex items-center py-1 gap-x-3 text-gray-800 text-base font-semibold; }
#mobile-nav > ul > li > .parent-icon,
#mobile-nav > ul > li > .nav-item > .parent-icon { @apply ml-8 text-base; }
#mobile-nav > ul > li > ul > li > .nav-item { @apply text-gray-700 text-base md:text-base font-medium; }

#mobile-nav ul.mobile-topmenu { @apply space-y-1; }
#mobile-nav ul.mobile-topmenu > li > .nav-item { @apply py-0 font-medium text-gray-500; }

[data-name="navbar-8"] #main-nav > ul { @apply flex gap-x-6 xl:gap-x-8 2xl:gap-x-12; }

footer .footer-menu .nav-item { @apply inline-flex text-lg md:text-sm lg:text-base text-gray-600 hover:text-gray-800 group-hover/menuitem:text-gray-800 dark:text-gray-300 dark:hover:text-gray-100; }

footer .footer-menu > ul { @apply flex flex-col space-y-6 md:space-y-0 md:flex-row md:items-center md:gap-x-6 lg:gap-x-8; }
footer .footer-menu > ul > li { @apply text-center md:text-left; }

footer[data-name="footer-4"] .footer-menu > ul,
footer[data-name="footer-5"] .footer-menu > ul,
footer[data-name="footer-8"] .footer-menu > ul { @apply flex flex-col md:flex-col space-y-2 md:space-y-1 items-start md:items-start; }
footer[data-name="footer-4"] .footer-menu > ul { @apply items-center md:items-center; }

footer[data-name="footer-8"] .footer-menu > ul > li { @apply leading-relaxed text-left md:text-left; }
footer[data-name="footer-4"] .footer-menu > ul > li { @apply leading-relaxed text-center md:items-center; }

footer[data-name="footer-4"] .footer-menu .nav-item,
footer[data-name="footer-5"] .footer-menu .nav-item,
footer[data-name="footer-8"] .footer-menu .nav-item { @apply text-base md:text-sm lg:text-base; }
