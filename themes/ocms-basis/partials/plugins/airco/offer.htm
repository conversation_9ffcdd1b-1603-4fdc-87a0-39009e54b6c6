<div>

    <div class="fixed z-[9999] inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" x-id="['modal-title']" :aria-labelledby="$id('modal-title')" x-show="open" x-cloak x-on:keydown.escape.prevent.stop="close">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div aria-hidden="true"
                x-show="open"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                @click="close"></div>

            {# This element is to trick the browser into centering the modal contents. #}
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            {# Modal panel, show/hide based on modal state. #}
            <div
                x-show="open"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6"
                @click="close"
            >

                {# Modal content #}

                <div class="sm:flex sm:flex-col sm:items-start" x-on:click.stop x-trap.noscroll.inert="open">
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">{{ 'Offerte aanvragen'|_ }}</h2>
                    </div>
                    {% ajaxPartial 'site/dynamic-form' formcode="contactformulier-2" %}
                </div>

                {# Modal footer #}

            </div>
        </div>
    </div>
</div>
