{% for item in item %}
    <div class="{{ stepClass }}">
        {% if loop.first %}
            {% set stepLine = 'w-1/2 left-1/2 lg:w-px lg:left-auto' %}
        {% elseif loop.last %}
            {% set stepLine = 'w-1/2 md:w-1/2 lg:w-px left-0 md:left-auto md:right-1/2 hidden md:block lg:hidden' %}
        {% else %}
            {% set stepLine = '' %}
        {% endif %}

        {% partial 'atomic/molecules/how-it-works/step-w-line' %}

        <div class="{{ cardWrapper }}">
            {% partial 'atomic/molecules/cards/card-sm'
                item=item
                cardClass="pb-4 pt-2.5 md:px-4"
                iconClass="text-4xl dark:text-gray-200"
                contentClass="prose-sm"
                truncate=100 %}
        </div>
    </div>
{% endfor %}