[staticMenu footerMenu]
code = "footer-menu"

[staticMenu footerMenu2]
code = "footer-menu"

[staticMenu footerMenu3]
code = "footer-menu"
==
{% set footerMenu1Items = footerMenu.resetMenu(footer.footer_menu_1) %}
{% set footerMenu2Items = footerMenu2.resetMenu(footer.footer_menu_2) %}
{% set footerMenu3Items = footerMenu3.resetMenu(footer.footer_menu_3) %}

{% partial 'atomic/organisms/footers/footer-wrap' name="footer-8" body %}
    <div class="container">
        <div class="py-8 space-y-8 md:space-y-0 md:grid md:grid-cols-12 md:gap-8">

            <div class="md:col-span-4 lg:col-span-3 xl:col-span-2 space-y-3">
                {% partial 'atomic/atoms/footer/heading' text=footer.footer_menu_1_title %}
                {% partial 'atomic/molecules/footer/nav' menuItems=footerMenu1Items %}
            </div>

            <div class="md:col-span-4 lg:col-span-3 xl:col-span-2 space-y-3">
                {% partial 'atomic/atoms/footer/heading' text=footer.footer_menu_2_title %}
                {% partial 'atomic/molecules/footer/nav' menuItems=footerMenu2Items %}
            </div>

            <div class="md:col-span-4 lg:col-span-3 xl:col-span-2 space-y-3">
                {% partial 'atomic/atoms/footer/heading' text=footer.footer_menu_3_title %}
                {% partial 'atomic/molecules/footer/nav' menuItems=footerMenu3Items %}
            </div>

            <div class="border-t md:col-span-12 lg:hidden"></div>

            <div class="md:col-span-6 lg:col-span-3">
                {% partial 'atomic/atoms/footer/heading' text="Contact" %}
                <div class="mt-3">
                    {% partial 'atomic/molecules/footer/contact-info' %}
                </div>
                {% if socials %}
                    <div class="flex mt-6">
                        {% partial 'atomic/molecules/footer/social-media' %}
                    </div>
                {% endif %}
            </div>

            <div class="border-t md:hidden lg:block md:col-span-12 xl:hidden"></div>

            <div class="md:col-span-6 lg:col-span-12 xl:col-span-3 lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center xl:block">
                <div>
                    {% if footer.newsletter_title %}
                        {% partial 'atomic/atoms/footer/heading' text=footer.newsletter_title %}
                    {% endif %}
                    {% if footer.newsletter_text %}
                        <div class="mt-3 mb-6 lg:mb-0 xl:mb-6">
                            <div class="prose prose-sm prose-primary dark:prose-primary_inverted max-w-none">
                                {{ footer.newsletter_text | content }}
                            </div>
                        </div>
                    {% endif %}
                </div>
                {% if not footer.hide_newsletter %}
                    <div class="md:flex lg:justify-end">
                        {% partial 'atomic/molecules/footer/newsletter-form' small=true %}
                    </div>
                {% endif %}
            </div>

        </div>

        {% if avg.tos_slug or avg.tos_media or avg.privacy_slug or avg.privacy_media %}
            <div class="pt-4 pb-8 md:border-t dark:md:border-gray-900/20 lg:hidden">
                <div class="space-y-3 md:space-y-0 md:flex md:justify-between md:items-center md:text-sm">
                    <div>
                        {% partial 'atomic/atoms/footer/tos' %}
                    </div>
                    <div>
                        {% partial 'atomic/atoms/footer/privacy' %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
    <div class="py-4 bg-gray-100 border-t dark:bg-gray-900/40 dark:md:border-gray-900/20">
        <div class="container">
            <div class="flex justify-between items-center text-sm text-gray-500">
                {% if avg.tos_slug or avg.tos_media or avg.privacy_slug or avg.privacy_media %}
                    <div class="hidden lg:flex space-x-4 items-center text-sm">
                        <div>
                            {% partial 'atomic/atoms/footer/tos' %}
                        </div>
                        <div>
                            {% partial 'atomic/atoms/footer/privacy' %}
                        </div>
                    </div>
                {% endif %}

                <div class="flex w-full md:w-auto justify-between md:justify-normal space-x-8 dark:text-gray-100">
                    {% partial 'atomic/atoms/footer/copyright' %}
                    {% partial 'atomic/atoms/footer/powered-by' %}
                </div>
            </div>
        </div>
    </div>
{% endpartial %}
