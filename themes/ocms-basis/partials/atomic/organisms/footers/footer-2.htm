[staticMenu footerMenu]
code = "footer-menu"
==
{% set footerMenuItems = footerMenu.resetMenu(footer.footer_menu_1) %}
{% set halfLength = footerMenuItems|length / 2 %}
{% set midPoint = halfLength|round %}

{% set leftMenu = footerMenuItems|slice(0, midPoint) %}
{% set rightMenu = footerMenuItems|slice(midPoint) %}


{% partial 'atomic/organisms/footers/footer-wrap' name="footer-2" body %}
    <div class="container">
        <div class="space-y-12 md:space-y-0 md:flex md:justify-between md:items-center py-12 md:py-4 border-b dark:border-gray-700">
            <div class="flex items-center md:w-auto justify-center md:justify-normal">
                {% partial 'atomic/atoms/footer/logo' %}
            </div>
            <div class="hidden md:block md:order-first md:flex-1">
                {% partial 'atomic/molecules/footer/nav' menuItems=leftMenu %}
            </div>
            <div class="hidden md:flex md:justify-end md:flex-1">
                {% partial 'atomic/molecules/footer/nav' menuItems=rightMenu %}
            </div>
            <div class="md:hidden">
                {% partial 'atomic/molecules/footer/nav' menuItems=footerMenuItems %}
            </div>
        </div>

        <div class="">
            {% partial 'atomic/molecules/footer/legal-bar' %}
        </div>
    </div>
</footer>
{% endpartial %}
