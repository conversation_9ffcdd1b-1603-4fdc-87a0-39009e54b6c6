[staticMenu mainMenu]
code = "main-menu"
==
{% set mainmenuItems = mainMenu.resetMenu(navbar.mainmenu) %}

{% partial 'atomic/organisms/navbars/navbar-wrap' name="navbar-2" body %}

    <div class="flex lg:justify-between items-center gap-x-8 {{ navbar.container == 'contained' ? 'container' }}">

        <div class="relative shrink-0 flex items-center">
            {% partial 'atomic/atoms/navbar/logo' %}
        </div>

        <div class="hidden lg:block">
            {% partial 'atomic/molecules/navbar/nav' %}
        </div>

        <div class="ml-auto lg:hidden">
            {% partial 'atomic/atoms/navbar/bars' icon="bars-staggered" %}
        </div>

    </div>

    {% partial 'atomic/organisms/navbars/mobile' %}

{% endpartial %}
