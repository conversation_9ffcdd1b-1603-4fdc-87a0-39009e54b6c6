{% set sortedPosts = posts | sort((a, b) => b.publication_date <=> a.publication_date) %}
{% for item in sortedPosts | slice(0, 3) %}
    <a href="{{ postPage | page({ slug: item.slug }) }}" class="flex justify-between blog-item-title prose prose-primary hover:text-primary dark:prose-primary_inverted max-w-none py-2">

        {% if item.title_short %}
            {% set blogTitle = item.title_short %}
        {% else %}
            {% set blogTitle = item.title %}
        {% endif %}

        <span class="{{ item.status == 'draft' ? 'flex gap-2' }}">
            {% if item.status == 'draft' %}
                <div class="flex items-center">
                    {% partial 'atomic/atoms/blog/concept' %}
                </div>
            {% endif %}
            {% partial 'atomic/atoms/cards/card-heading' text=blogTitle level=3 %}
        </span>

        <span class="px-2"><i class="fa-regular fa-chevron-right"></i></span>
    </a>
{% endfor %}