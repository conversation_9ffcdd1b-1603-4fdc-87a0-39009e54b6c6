<div class=" {{ cardClass }}">
    <div class="flex justify-center z-10">
        {% partial 'atomic/atoms/how-it-works/number-sm' text=index %}
    </div>
    <div class="card-content {{ cardContentClass|default('gap-2') }} pt-1.5">
        {% if item.title %}
            <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                {% partial 'atomic/atoms/headings/header-h3' text=item.title %}
            </div>
        {% endif %}

        {% if item.content_card %}
            {% partial 'atomic/atoms/content-card' text=item.content_card class=contentClass truncate=truncate %}
        {% endif %}
    </div>
</div>
