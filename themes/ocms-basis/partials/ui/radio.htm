<div>
  <div class="flex items-center justify-between">
    <h2 class="text-sm font-medium text-gray-900">RAM</h2>
    <a href="#" class="text-sm font-medium text-orange-600 hover:text-orange-500">See performance specs</a>
  </div>

  <fieldset
    x-data="radioButtonGroup('4 GB')"
    @keydown.down.stop.prevent="selectNext"
    @keydown.right.stop.prevent="selectNext"
    @keydown.up.stop.prevent="selectPrevious"
    @keydown.left.stop.prevent="selectPrevious"
    role="radiogroup"
    :aria-labelledby="$id('radio-group-label')"
    x-id="['radio-group-label']"
    class="mt-2 mb-8"
  >
    <legend class="sr-only">Choose a memory option</legend>
    <div class="grid grid-cols-3 gap-3 sm:grid-cols-6">
      <label
        x-data="{ option: '4 GB' }"
        @click="select(option)"
        @keydown.enter.stop.prevent="select(option)"
        @keydown.space.stop.prevent="select(option)"
        :aria-checked="isSelected(option)"
        :tabindex="hasRovingTabindex(option, $el) ? 0 : -1"
        :aria-labelledby="$id('radio-option-label')"
        :aria-describedby="$id('radio-option-description')"
        x-id="['radio-option-label', 'radio-option-description']"
        role="radio"
        class="border rounded-md py-3 px-3 flex items-center justify-center text-sm font-medium uppercase sm:flex-1 cursor-pointer focus:outline-none"
        :class="isSelected(option) ? 'focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 ring-2 ring-offset-2 ring-orange-500 bg-orange-600 border-transparent text-white hover:bg-orange-700' : 'bg-white border-gray-200 text-gray-900 hover:bg-gray-50'"
      >
        <input type="radio" name="memory-option" value="4 GB" class="sr-only" aria-labelledby="memory-option-0-label">
        <p id="memory-option-0-label">4 GB</p>
      </label>

      <label
        x-data="{ option: '8 GB' }"
        @click="select(option)"
        @keydown.enter.stop.prevent="select(option)"
        @keydown.space.stop.prevent="select(option)"
        :aria-checked="isSelected(option)"
        :tabindex="hasRovingTabindex(option, $el) ? 0 : -1"
        :aria-labelledby="$id('radio-option-label')"
        :aria-describedby="$id('radio-option-description')"
        x-id="['radio-option-label', 'radio-option-description']"
        role="radio"
        class="border rounded-md py-3 px-3 flex items-center justify-center text-sm font-medium uppercase sm:flex-1 cursor-pointer focus:outline-none"
        :class="isSelected(option) ? 'focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 ring-2 ring-offset-2 ring-orange-500 bg-orange-600 border-transparent text-white hover:bg-orange-700' : 'bg-white border-gray-200 text-gray-900 hover:bg-gray-50'"
      >
        <input type="radio" name="memory-option" value="8 GB" class="sr-only" aria-labelledby="memory-option-0-label">
        <p id="memory-option-0-label">8 GB</p>
      </label>

      <label
        x-data="{ option: '16 GB' }"
        @click="select(option)"
        @keydown.enter.stop.prevent="select(option)"
        @keydown.space.stop.prevent="select(option)"
        :aria-checked="isSelected(option)"
        :tabindex="hasRovingTabindex(option, $el) ? 0 : -1"
        :aria-labelledby="$id('radio-option-label')"
        :aria-describedby="$id('radio-option-description')"
        x-id="['radio-option-label', 'radio-option-description']"
        role="radio"
        class="border rounded-md py-3 px-3 flex items-center justify-center text-sm font-medium uppercase sm:flex-1 cursor-pointer focus:outline-none"
        :class="isSelected(option) ? 'focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 ring-2 ring-offset-2 ring-orange-500 bg-orange-600 border-transparent text-white hover:bg-orange-700' : 'bg-white border-gray-200 text-gray-900 hover:bg-gray-50'"
      >
        <input type="radio" name="memory-option" value="16 GB" class="sr-only" aria-labelledby="memory-option-0-label">
        <p id="memory-option-0-label">16 GB</p>
      </label>

      <label
        x-data="{ option: '32 GB' }"
        @click="select(option)"
        @keydown.enter.stop.prevent="select(option)"
        @keydown.space.stop.prevent="select(option)"
        :aria-checked="isSelected(option)"
        :tabindex="hasRovingTabindex(option, $el) ? 0 : -1"
        :aria-labelledby="$id('radio-option-label')"
        :aria-describedby="$id('radio-option-description')"
        x-id="['radio-option-label', 'radio-option-description']"
        role="radio"
        class="border rounded-md py-3 px-3 flex items-center justify-center text-sm font-medium uppercase sm:flex-1 cursor-pointer focus:outline-none"
        :class="isSelected(option) ? 'focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 ring-2 ring-offset-2 ring-orange-500 bg-orange-600 border-transparent text-white hover:bg-orange-700' : 'bg-white border-gray-200 text-gray-900 hover:bg-gray-50'"
      >
        <input type="radio" name="memory-option" value="32 GB" class="sr-only" aria-labelledby="memory-option-0-label">
        <p id="memory-option-0-label">32 GB</p>
      </label>
    </div>
  </fieldset>

  <fieldset
    x-data="radioButtonGroup('Pink')"
    @keydown.down.stop.prevent="selectNext"
    @keydown.right.stop.prevent="selectNext"
    @keydown.up.stop.prevent="selectPrevious"
    @keydown.left.stop.prevent="selectPrevious"
    role="radiogroup"
    :aria-labelledby="$id('radio-group-label')"
    x-id="['radio-group-label']"
  >
  <legend class="block text-sm font-medium text-gray-700">Choose a label color</legend>
  <div class="mt-4 flex items-center space-x-3">
    <label
        x-data="{ option: 'Pink' }"
        @click="select(option)"
        @keydown.enter.stop.prevent="select(option)"
        @keydown.space.stop.prevent="select(option)"
        :aria-checked="isSelected(option)"
        :tabindex="hasRovingTabindex(option, $el) ? 0 : -1"
        :aria-labelledby="$id('radio-option-label')"
        :aria-describedby="$id('radio-option-description')"
        x-id="['radio-option-label', 'radio-option-description']"
        role="radio"
        class="-m-0.5 relative p-0.5 rounded-full flex items-center justify-center cursor-pointer focus:outline-none ring-pink-500"
        :class="{'ring-2 ring-offset-1 focus:ring': isSelected(option)}"
    >
      <input type="radio" name="color-choice" value="Pink" class="sr-only" aria-labelledby="color-choice-0-label">
      <p id="color-choice-0-label" class="sr-only">Pink</p>
      <span aria-hidden="true" class="h-8 w-8 bg-pink-500 border border-black border-opacity-10 rounded-full"></span>
    </label>

    <label
        x-data="{ option: 'Purple' }"
        @click="select(option)"
        @keydown.enter.stop.prevent="select(option)"
        @keydown.space.stop.prevent="select(option)"
        :aria-checked="isSelected(option)"
        :tabindex="hasRovingTabindex(option, $el) ? 0 : -1"
        :aria-labelledby="$id('radio-option-label')"
        :aria-describedby="$id('radio-option-description')"
        x-id="['radio-option-label', 'radio-option-description']"
        role="radio"
        class="-m-0.5 relative p-0.5 rounded-full flex items-center justify-center cursor-pointer focus:outline-none ring-purple-500"
        :class="{'ring-2 ring-offset-1 focus:ring': isSelected(option)}"
    >
      <input type="radio" name="color-choice" value="Purple" class="sr-only" aria-labelledby="color-choice-0-label">
      <p id="color-choice-0-label" class="sr-only">Purple</p>
      <span aria-hidden="true" class="h-8 w-8 bg-purple-500 border border-black border-opacity-10 rounded-full"></span>
    </label>

    <label
        x-data="{ option: 'Blue' }"
        @click="select(option)"
        @keydown.enter.stop.prevent="select(option)"
        @keydown.space.stop.prevent="select(option)"
        :aria-checked="isSelected(option)"
        :tabindex="hasRovingTabindex(option, $el) ? 0 : -1"
        :aria-labelledby="$id('radio-option-label')"
        :aria-describedby="$id('radio-option-description')"
        x-id="['radio-option-label', 'radio-option-description']"
        role="radio"
        class="-m-0.5 relative p-0.5 rounded-full flex items-center justify-center cursor-pointer focus:outline-none ring-blue-500"
        :class="{'ring-2 ring-offset-1 focus:ring': isSelected(option)}"
    >
      <input type="radio" name="color-choice" value="Blue" class="sr-only" aria-labelledby="color-choice-0-label">
      <p id="color-choice-0-label" class="sr-only">Blue</p>
      <span aria-hidden="true" class="h-8 w-8 bg-blue-500 border border-black border-opacity-10 rounded-full"></span>
    </label>

    <label
        x-data="{ option: 'Green' }"
        @click="select(option)"
        @keydown.enter.stop.prevent="select(option)"
        @keydown.space.stop.prevent="select(option)"
        :aria-checked="isSelected(option)"
        :tabindex="hasRovingTabindex(option, $el) ? 0 : -1"
        :aria-labelledby="$id('radio-option-label')"
        :aria-describedby="$id('radio-option-description')"
        x-id="['radio-option-label', 'radio-option-description']"
        role="radio"
        class="-m-0.5 relative p-0.5 rounded-full flex items-center justify-center cursor-pointer focus:outline-none ring-green-500"
        :class="{'ring-2 ring-offset-1 focus:ring': isSelected(option)}"
    >
      <input type="radio" name="color-choice" value="Green" class="sr-only" aria-labelledby="color-choice-0-label">
      <p id="color-choice-0-label" class="sr-only">Green</p>
      <span aria-hidden="true" class="h-8 w-8 bg-green-500 border border-black border-opacity-10 rounded-full"></span>
    </label>

    <label
        x-data="{ option: 'Yellow' }"
        @click="select(option)"
        @keydown.enter.stop.prevent="select(option)"
        @keydown.space.stop.prevent="select(option)"
        :aria-checked="isSelected(option)"
        :tabindex="hasRovingTabindex(option, $el) ? 0 : -1"
        :aria-labelledby="$id('radio-option-label')"
        :aria-describedby="$id('radio-option-description')"
        x-id="['radio-option-label', 'radio-option-description']"
        role="radio"
        class="-m-0.5 relative p-0.5 rounded-full flex items-center justify-center cursor-pointer focus:outline-none ring-yellow-500"
        :class="{'ring-2 ring-offset-1 focus:ring': isSelected(option)}"
    >
      <input type="radio" name="color-choice" value="Yellow" class="sr-only" aria-labelledby="color-choice-0-label">
      <p id="color-choice-0-label" class="sr-only">Yellow</p>
      <span aria-hidden="true" class="h-8 w-8 bg-yellow-500 border border-black border-opacity-10 rounded-full"></span>
    </label>
    
  </div>
</fieldset>
</div>


