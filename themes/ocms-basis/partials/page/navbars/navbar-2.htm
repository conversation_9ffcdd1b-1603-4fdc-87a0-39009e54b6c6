[staticMenu mainMenu]
code = "main-menu"
==
{% set mainmenuItems = mainMenu.resetMenu(this.theme.navbar.site_primary_menu) %}

<header id="navbar-2" class="{{ this.page.layout == 'hero' ? 'navbar navbar-hero' : 'navbar' }}">
    <div class="container">
        <div class="navbar-row flex flex-wrap items-center justify-between">
            <div class="logo">
                {% partial 'ui/logo/logo_primair' %}
            </div>
            <nav class="mainmenu" x-data="{ open: false }">
                {% partial 'page/mainmenus/mainmenu-trigger' %}
                {% partial 'page/mainmenus/mainmenu' mainmenuItems=mainmenuItems %}
                {% partial 'page/mobilemenus/mobilemenu' mainmenuItems=mainmenuItems %}
            </nav>
        </div>
    </div>
</header>
