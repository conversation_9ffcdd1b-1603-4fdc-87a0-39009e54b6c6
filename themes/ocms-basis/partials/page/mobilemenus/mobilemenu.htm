<div class="mobilemenu-wrapper lg:hidden" x-show="open" x-cloak>
    <div class="mobilemenu-inner" @click.outside="open = false">
        <div class="flex justify-end">
            <button type="button" class="close-button" @click="open = false">
                <i class="fa-solid fa-xmark  fa-xl"></i>
            </button>
        </div>
        <div class="mobilemenu-content flex flex-col h-full">
            <div class="mobilemenu">
                <ul>
                    {% for item in mainmenuItems %}
                        <li class="group {{ item.items ? 'hasSubmenu' }} {{ item.isActive or item.isChildActive ? 'active' }}" x-data="{ submenu: false }">
                            {% if item.items %}
                                <div class="mobilemenu-item">
                                    <a href="{{ item.url }}" class="flex-1">{{ item.title }}</a>
                                    <button class="button" @click="submenu = ! submenu">
                                        <span x-show="submenu"><i class="fa-solid fa-chevron-up"></i></span>
                                        <span x-show="! submenu"><i class="fa-solid fa-chevron-down"></i></span>
                                    </button>
                                </div>
                                <ul class="submenu" x-show="submenu" x-collapse x-cloak>
                                    {% for subitem in item.items %}
                                        <li class="group {{ subitem.isActive ? 'active' }}">
                                            <a href="{{ subitem.url }}">{{ subitem.title }}</a>
                                        </li>
                                    {% endfor %}
                                </ul>
                            {% else %}
                                <a href="{{ item.url }}" class="flex-1">{{ item.title }}</a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            </div>
            <div class="mobilemenu-contact">
                {% partial 'ui/logo/logo_mobile' %}
                {% partial 'ui/contact_info_basic' %}
            </div>
        </div>
    </div>
</div>
