<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="how-it-works-9"
    data-category="how-it-works"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

	<div class="lg:max-w-7xl mx-auto md:space-y-12 lg:space-y-0">

        {% if box.title or box.subtitle or box.content %}
            <div class="hidden md:block lg:hidden px-4">
                {% partial 'atomic/molecules/prose/prose-primary' body %}
                {% partial 'atomic/molecules/content-heading' %}
                {% partial 'atomic/molecules/content-section' content=box.content %}
                {% endpartial %}
            </div>
        {% endif %}

        <div class="space-y-8 md:space-y-0 md:grid md:grid-cols-2 gap-8">

            <div class="flex flex-col gap-8 px-4 {{ box.flip_content ? 'order-last' : 'order-first' }}">

                {% if box.title or box.subtitle or box.content %}
                    <div class="md:hidden lg:block ">
                        {% partial 'atomic/molecules/prose/prose-primary' body %}
                        {% partial 'atomic/molecules/content-heading' %}
                        {% partial 'atomic/molecules/content-section' content=box.content %}
                        {% endpartial %}
                    </div>
                {% endif %}

                <div class="space-y-8 ">
                    {% for item in box.steps_sm %}

                        {% partial 'atomic/molecules/how-it-works/hiw-card-sm'
                            item=item
                            index=loop.index
                            cardClass="flex space-x-4"
                            cardContentClass="space-y-2"
                            contentClass="line-clamp-5"
                            buttonClass=""
                            truncate=250 %}

                    {% endfor %}
                </div>

            </div>


            <div class="flex h-full overflow-hidden {{ box.flip_content ? 'order-first md:rounded-r lg:rounded' : 'order-last md:rounded-l lg:rounded' }}">
                {% partial 'atomic/atoms/media/image' img=box.img title=box.img_title resize_h='1800' class='w-full h-full object-cover' %}
            </div>

        </div>

    </div>

</section>