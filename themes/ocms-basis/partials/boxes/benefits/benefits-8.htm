<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="benefits-8"
    data-category="benefits"
    class="benefits relative {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    {% if box._boxes_spacing_after == 'large' %}
        {% set negativeTopMargin = "-mb-12 md:-mb-16 lg:-mb-24 xl:mb-0" %}
    {% elseif box._boxes_spacing_after == 'medium' %}
        {% set negativeTopMargin = "-mb-12 md:-mb-12 lg:-mb-16 xl:mb-0" %}
    {% elseif box._boxes_spacing_after == 'small' %}
        {% set negativeTopMargin = "-mb-10 xl:mb-0" %}
    {% endif %}

    <div class="container relative z-20 mb-12 xl:mb-0">
        <div class="xl:flex xl:flex-wrap {{ box.flip_content ? 'xl:justify-end' : 'xl:justify-start' }}">
            <div class="w-full xl:w-1/2 px-4 {{ box.flip_content ? 'xl:pl-12' : 'xl:pr-12' }}">
                <div class="w-full md:w-2/3">
                    {% if box.title or box.content %}
                        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                            {% partial 'atomic/molecules/content-heading' %}
                            {% partial 'atomic/molecules/content-section' content=box.content %}
                        </div>
                    {% endif %}
                    {% if box.buttons %}
                        {% partial 'atomic/molecules/buttons' buttons=box.buttons class="buttons-wrap" %}
                    {% endif %}
                </div>
                <div class="w-full space-y-8 md:space-y-0 md:grid md:grid-cols-2 xl:grid-cols-none xl:grid-rows-2 xl:grid-flow-col md:gap-8 mt-8">
                    {% for item in box.features_sm|slice(0,4) %}
                        <div class="{{ box.features_sm|length < 3 ? 'xl:w-1/2' }}">
                            {% partial 'atomic/molecules/cards/card-sm'
                                item=item
                                cardClass='bg-white dark:bg-white/5 p-4'
                                iconClass='text-4xl dark:text-white'%}
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="{{ negativeTopMargin }} xl:absolute xl:inset-0 xl:z-10 xl:flex {{ box.flip_content ? 'xl:justify-start' : 'xl:justify-end' }}">
        <div class="w-full xl:w-1/2 xl:h-full">
            {% partial 'atomic/atoms/media/image' img=box.img title=box.img_title resize_w="1200" class="w-full h-full object-cover" %}
        </div>
    </div>
</section>
