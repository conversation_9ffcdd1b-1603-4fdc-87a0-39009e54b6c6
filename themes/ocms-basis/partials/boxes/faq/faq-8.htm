<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="faq-8"
    data-category="faq"
    class="faq {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    <div class="max-w-7xl mx-auto md:px-4">
        <div class="space-y-12">
            <div class="px-4 md:px-0">
                {% if box.title or box.subtitle or box.content %}
                    {% partial 'atomic/molecules/content-heading-centered' box=box %}
                {% endif %}

                {% if box.buttons %}
                    {% partial 'atomic/molecules/buttons' buttons=box.buttons class="flex justify-center space-x-4" %}
                {% endif %}
            </div>

            <div>
                <dl class="faq-list">
                    {% for key, item in box.questions %}
                        <div class="faq-item">
                            <dt>
                                <div class="faq-question">
                                    {{ item.question }}
                                </div>
                            </dt>
                            <dd class="faq-answer">
                                <div class="faq-answer-inner prose-sm prose-primary dark:prose-primary_inverted max-w-none">
                                    {{ item.answer | content }}
                                </div>
                            </dd>
                        </div>
                    {% endfor %}
                </dl>
            </div>
        </div>
    </div>
</section>
