<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="faq-3"
    data-category="faq"
    class="faq {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    <div class="container">
        <div class="space-y-12">
            <div class="px-4 md:px-0">
                {% if box.title or box.subtitle or box.content %}
                    {% partial 'atomic/molecules/content-heading-centered' box=box %}
                {% endif %}

                {% if box.buttons %}
                    {% partial 'atomic/molecules/buttons' buttons=box.buttons class="flex justify-center space-x-4" %}
                {% endif %}
            </div>

            <div x-data="{ active: 1 }">
                <dl class="faq-list">
                    {% for key, item in box.questions %}
                        {% partial 'atomic/molecules/faq/faq-item-collapse' body name="faq-3" %}
                            {% partial 'atomic/molecules/faq/faq-question-collapse' question=item.question reversed=true opened_icon="fa-regular fa-chevron-down" closed_icon="fa-regular fa-chevron-right" %}
                            {% partial 'atomic/molecules/faq/faq-answer-collapse' answer=item.answer %}
                        {% endpartial %}
                    {% endfor %}
                </dl>
            </div>
        </div>
    </div>
</section>
