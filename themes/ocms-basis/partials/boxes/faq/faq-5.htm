<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="faq-5"
    data-category="faq"
    class="faq {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    <div class="container">
        <div class="space-y-12">
            <div class="px-4 md:px-0">
                {% if box.title or box.subtitle or box.content %}
                    {% partial 'atomic/molecules/content-heading-centered' box=box %}
                {% endif %}

                {% if box.buttons %}
                    {% partial 'atomic/molecules/buttons' buttons=box.buttons class="flex justify-center space-x-4" %}
                {% endif %}
            </div>

            <div class="md:grid md:grid-cols-12 md:gap-8">
                <div class="md:col-start-2 md:col-span-10">
                    <dl class="faq-list">
                        {% for key, item in box.questions %}
                            <div class="faq-item">
                                <div class="flex gap-x-6">
                                    <div class="hidden sm:flex shrink-0 py-6 px-4">
                                        <div class="w-12 h-12 rounded-full bg-gray-500 text-white text-2xl flex items-center justify-center">
                                            <i class="fa-solid fa-question"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1 pt-3.5">
                                        <dt>
                                            <div class="faq-question">
                                                {{ item.question }}
                                            </div>
                                        </dt>
                                        <dd class="faq-answer">
                                            <div class="faq-answer-inner prose-sm prose-primary dark:prose-primary_inverted max-w-none">
                                                {{ item.answer | content }}
                                            </div>
                                        </dd>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </dl>
                </div>
            </div>
        </div>
    </div>
</section>
