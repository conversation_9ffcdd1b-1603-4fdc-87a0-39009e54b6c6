handle: content-10
name: Content 10
section: Content
icon: /app/assets/boxes/content/content-10.png
order: 200

spacing:
- general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            _ruler1:
                type: ruler
                tab: Algemeen
            buttons:
                type: mixin
                tab: Algemeen
            _ruler2:
                type: ruler
                tab: Algemeen
            cards-img:
                type: mixin
                tab: Algemeen
            background_color:
                type: mixin
                tab: Design
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design
