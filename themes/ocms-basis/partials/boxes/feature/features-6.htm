<section 
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %} 
    data-name="features-6"
    data-category="features" 
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}" 
    style="background-color: {{ box.background_color }};" 
    data-boxes-container 
    data-rel="boxes-wrapper">
    
	<div class="container space-y-8">

        {% if box.title or box.subtitle or box.content %}
            <div class="">
                {% partial 'atomic/molecules/content-heading-centered' box=box %}
            </div>
        {% endif %}
        
        <div class="space-y-8">

            <div class="flex flex-col justify-center space-y-8 md:space-y-0 md:flex-row md:flex-wrap -mx-4">

                {% for item in box.cards_sm %}

                    <div class="md:w-1/2 lg:w-1/3 xl:w-1/6 md:p-4">
                        {% partial 'atomic/molecules/cards/card-sm'
                            item=item
                            cardClass='bg-white items-center text-center rounded p-4 dark:bg-gray-500'
                            iconClass="text-4xl dark:text-gray-200"
                            contentClass="prose-sm"
                            truncate=40 %}
                    </div>

                {% endfor %}

            </div>

            {% if box.img %}
                <div class="hidden md:block w-full lg:aspect-[5/2] rounded overflow-hidden">
                    {% partial 'atomic/atoms/media/image' img=box.img title=box.img_title resize_w='1280' class='w-full h-full  object-cover' %}
                </div>
            {% endif %}

        </div>
        
    </div>
	
</section>