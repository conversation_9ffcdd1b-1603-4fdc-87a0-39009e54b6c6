{% set spaceTop = box._boxes_spacing_before %}
{% set spaceBot = box._boxes_spacing_after %}

<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="header-3-var"
    data-category="header"
    class="relative {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};">

    <div class="relative z-30" data-boxes-container data-rel="boxes-wrapper">
        <div class="container">
            <div class="lg:grid lg:grid-cols-2 lg:gap-24 lg:items-center">
                <div class="{{ spaceTop == 'none' ? 'pt-16' : 'pt-8 lg:pt-0' }} {{ spaceBot == 'none' ? 'pb-16' : 'pb-8 lg:pb-0' }} md:px-14 lg:px-4 xl:px-0">
                    <div class="prose prose-primary dark:prose-primary_inverted">
                        {# <p>{{ spaceTop }} - {{ spaceBot }}</p> #}
                        {% if box.title %}
                            <div class="dark:prose-strong:text-secondary-500">{% partial 'atomic/atoms/headings/header-h1' text=box.title %}</div>
                        {% endif %}
                        {% if box.content %}
                            {% partial 'atomic/molecules/content-section' content=box.content %}
                        {% endif %}
                    </div>
                    {% if box.buttons %}
                        {% partial 'atomic/molecules/buttons' buttons=box.buttons class="buttons-wrap" %}
                    {% endif %}
                </div>
            </div>
        </div>

    </div>

    <div class="w-full aspect-thumb md:aspect-video lg:aspect-auto select-none pointer-events-none lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2 py-8">
        <div class="rounded-l-full overflow-hidden h-full shadow-xl">
            {% partial 'atomic/atoms/media/image' img=box.img title=box.img_title resize_w="1270" class="w-full h-full object-cover" %}
        </div>
    </div>

</section>
