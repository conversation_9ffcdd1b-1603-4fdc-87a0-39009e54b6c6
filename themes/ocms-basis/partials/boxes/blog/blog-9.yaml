handle: blog-9
name: Blog 9
section: Blog
icon: /app/assets/boxes/blog/blog-9.png
order: 190

spacing:
    - general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            category:
                label: Categorie filter
                type: dropdown
                emptyOption: '-- geen categorie filter --'
                optionsMethod: getBlogCategoryOptions
                tab: Algemeen
            max_items:
                label: Maximaal aantal items
                type: dropdown
                default: 4
                options:
                    4: 4
                    8: 8
                tab: Algemeen
            background_color:
                type: mixin
                tab: Design
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design
