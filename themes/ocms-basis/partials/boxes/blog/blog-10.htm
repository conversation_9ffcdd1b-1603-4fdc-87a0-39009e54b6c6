[postList]

[categoryList]

[siteSearchInclude]
==
{% set allItems = postList.posts %}
{% set featuredPost = postList.featuredPost %}
{% set items = postListBox.posts %}
{% set blogPage = postList.blogPage|link %}
{% set postPage = postList.postPage %}
{% set categoryPage = postList.categoryPage %}
{% set categories = categoryList.categories %}

<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="blog-10"
    data-category="blog"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="container space-y-8 xl:space-y-12">
        <div class="space-y-8 md:space-y-0 md:grid md:grid-cols-3 md:gap-8 xl:grid-cols-4">
            <div class="prose prose-primary dark:prose-primary_inverted max-w-none xl:col-span-2">
                {% partial 'atomic/molecules/content-heading' box=box %}
            </div>
            {% if box.content %}
                <div class="prose prose-primary dark:prose-primary_inverted max-w-none md:col-span-2">
                    {% partial 'atomic/molecules/content-section' content=box.content truncate=truncate %}
                </div>
            {% endif %}
        </div>

        {% if categories.toArray() %}
            <div class="flex flex-col items-center justify-center space-y-4">
                <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                    {% partial 'atomic/atoms/headings/header-h4' text='Categorieën'|_ %}
                </div>
                <div class="flex flex-wrap items-center justify-center dark:bg-gray-300 border border-gray-400 rounded-lg p-1 gap-2">

                    {% for item in categories %}
                        {% if this.param.slug == item.slug %}
                            {% set activeStyle = 'bg-primary-100 hover:bg-primary-200 text-primary-900 dark:bg-primary-600 dark:hover:bg-primary-700 dark:text-primary-50' %}
                        {% else %}
                            {% set activeStyle = 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-gray-100' %}
                        {% endif %}
                        <a href="{{ categoryPage | page({ slug: item.slug }) }}" title="{{ item.title }}"
                           class="inline-block {{ activeStyle }} font-bold tracking-wide text-sm rounded-lg shadow-sm leading-none py-3 px-6">
                            {{ item.title }}
                        </a>
                    {% endfor %}

                </div>
            </div>
        {% endif %}

        <div class="max-w-2xl mx-auto space-y-8 md:space-y-0 md:grid gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 lg:max-w-none">
            {% if featuredPost %}
                <div class="md:hidden">
                    {% partial 'atomic/molecules/blog/blog-item-card' item=featuredPost categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
                </div>
                <div class="hidden md:block row-span-2 col-span-2">
                    {% partial 'atomic/molecules/blog/blog-item-card-large-sq' item=featuredPost categoryPage=categoryPage link=postPage | page({ slug: featuredPost.slug }) %}
                </div>
            {% endif %}

            {% for item in items %}
                {% partial 'atomic/molecules/blog/blog-item-card' imgHidden=true item=item categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
            {% endfor %}
        </div>

        <div class="">
            {{ pager(items, { partial: 'ui/pagination' }) }}
        </div>

    </div>

</section>
