<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="cta-8"
    data-category="call-to-actions"
    class="relative {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    {% if box.bg_image %}
        <div class="absolute inset-0 pointer-events-none z-10">
            {% if box.bg_overlay_color %}
                <div
                    class="absolute inset-0 z-30"
                    style="background-color: {{ box.bg_overlay_color }}; opacity: .{{ box.bg_overlay_alpha }};">
                </div>
            {% endif %}
            {% if box.bg_image %}
                {% partial 'atomic/atoms/media/image' img=box.bg_image resize_w="1920" class="w-full h-full object-cover relative z-20" %}
            {% endif %}
        </div>
    {% endif %}

    <div class="max-w-7xl mx-auto md:px-4 relative z-20">
        <div class="space-y-12">
            {% if box.title or box.subtitle or box.content %}
                <div class="px-4 md:px-0">
                    {% partial 'atomic/molecules/content-heading-centered' box=box %}
                </div>
            {% endif %}

            <div class="space-y-4">
                {% if not box.hide_form %}
                    <div class="lg:grid lg:grid-cols-12 lg:gap-8">
                        <div class="lg:col-span-10 lg:col-start-2">
                            {% partial 'atomic/molecules/newsletter-form' %}
                        </div>
                    </div>
                {% endif %}

                {% if box.hide_form %}
                    {% set noMargin = 'true' %}
                {% endif %}

                {% if socials %}
                    <div class="flex justify-center text-white">
                        {% partial 'ui/social_media' no_margin=noMargin %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>
