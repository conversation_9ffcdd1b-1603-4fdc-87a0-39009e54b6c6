[PortfolioProjectList]
itemsPerPage = "{{ box.itemsPerPage }}"
==
{% set projects = PortfolioProjectList.projects %}
{% set categories = PortfolioProjectList.categories %}
{% set projectPage = PortfolioProjectList.projectPage %}
{% set categoryPage = PortfolioProjectList.categoryPage %}
{% set portfolioPage = PortfolioProjectList.portfolioPage %}
{% set portfolioCategory = PortfolioProjectList.category %}
<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="portfolio-3"
    data-category="portfolio"
    class="relative {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    <div class="container  space-y-8 xl:space-y-12">

        {% if box.title or box.subtitle or box.content %}
            <div class="space-y-2">
                <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                    {% partial 'atomic/molecules/content-heading-centered' box=box %}
                </div>
            </div>
        {% endif %}

        {% if categories.toArray() %}
            <div class="flex flex-col items-center justify-center space-y-4">
                <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                    {% partial 'atomic/atoms/headings/header-h4' text='Categorieën'|_ %}
                </div>
                <div class="flex flex-wrap items-center justify-center dark:bg-gray-300 border border-gray-400 rounded-lg p-1 gap-2">

                    {% for item in categories %}
                        {% if this.param.slug == item.slug %}
                            {% set activeStyle = 'bg-primary-100 hover:bg-primary-200 text-primary-900 dark:bg-primary-600 dark:hover:bg-primary-700 dark:text-primary-50' %}
                        {% else %}
                            {% set activeStyle = 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-gray-100' %}
                        {% endif %}
                        <a href="{{ categoryPage | page({ slug: item.slug }) }}" title="{{ item.title }}"
                           class="inline-block {{ activeStyle }} font-bold tracking-wide text-sm rounded-lg shadow-sm leading-none py-3 px-6">
                            {{ item.title }}
                        </a>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <div class="space-y-8 md:space-y-0 md:grid lg:grid-flow-row-dense md:grid-cols-2 lg:grid-cols-4 gap-4 py-4 md:p-4">
            {% for item in projects %}
                {% if loop.first %}
                    {% partial 'atomic/molecules/portfolio/project-masonry' item=item projectPage=projectPage card_size=card_size project_class="lg:col-span-2 lg:row-span-1" %}
                {% elseif loop.index == "5" %}
                    {% partial 'atomic/molecules/portfolio/project-masonry' item=item projectPage=projectPage card_size=card_size project_class="lg:col-span-1 lg:row-span-2" %}
                {% elseif loop.index == "6" %}
                    {% partial 'atomic/molecules/portfolio/project-masonry' item=item projectPage=projectPage card_size=card_size project_class="lg:col-span-2 lg:row-span-2" %}
                {% else %}
                    {% partial 'atomic/molecules/portfolio/project-masonry' item=item projectPage=projectPage card_size=card_size project_class="lg:col-span-1 lg:row-span-1" %}
                {% endif %}
            {% endfor %}
        </div>

        <div class="">
            {{ pager(projects, { partial: 'ui/pagination' }) }}
        </div>

    </div>
</section>
