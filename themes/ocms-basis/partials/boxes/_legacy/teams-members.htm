[MemberList]

[siteSearchInclude]
==
{% set members = MemberList.members %}
{% set teamPage = TeamsList.teamPage %}
{% set departmentPage = TeamsList.departmentPage %}
{% set memberPage = TeamsList.memberPage %}

<div id="teams-department" class="" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container space-y-20">
        <div class="text-center space-y-4">
            <div class="">
                <h2>{{ box.title }}</h2>
            </div>
            <div class="content_section">
                {{ box.content | content }}
            </div>
        </div>

        {% if box.grid_cols == '2 Kolommen' %}
            {% set gridCols = 'md:grid-cols-2 gap-8 lg:gap-16' %}
            {% set cardPadding = 'p-4 lg:p-8' %}
        {% elseif box.grid_cols == '4 Kolommen' %}
            {% set gridCols = 'md:grid-cols-2 lg:grid-cols-4 gap-8' %}
            {% set cardPadding = 'p-4 lg:py-8 lg:px-6' %}
        {% else %}
            {% set gridCols = 'md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-16' %}
            {% set cardPadding = 'p-4 lg:p-8' %}
        {% endif %}

        <div class="md:grid {{ gridCols }}">
            {% for member in members %}
            <div class="rounded-xl border border-gray-300 shadow-xl bg-white">
                <div class="flex flex-col justify-center space-y-4 {{ cardPadding }}">
                    <div class="overflow-hidden mx-auto mb-4">
                        <img src="{{ member.img | media | resize(600, auto, { 'extension': 'webp' }) }}"
                             alt="{{ member.name }} {{ member.surname }}" class="h-32 w-32 rounded-full object-cover">
                    </div>
                    <div class="">
                        <div class="text-lg font-bold hover:text-gray-500">
                            {{ member.name }}
                            {% if member.name or member.surname %}
                            <span> </span>
                            {% endif %}
                            {{ member.surname }}
                        </div>
                        <div class="flex flex-wrap justify-between"><p class="font-medium text-primary-600">{{ member.title }}</p>
                            {% if member.socials %}
                            <div class="flex space-x-4">
                                {% for social in member.socials %}
                                <div class="">
                                    <a href="{{ social.slug }}" target="_blank">
                                        <span class="sr-only">{{ social.title }}</span>
                                        <i class="fab fa-{{ social.title }} text-lg text-gray-400 hover:text-gray-500"></i>
                                    </a>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}</div>
                    </div>

                    <div class="content_section mt-6" x-data="{ expanded: false }">
                        <div :class="{ 'mb-4': !(expanded) }" x-show="expanded" x-collapse.min.136px>{{ member.description | content }}</div>
                        <button @click="expanded = ! expanded" class="font-bold text-primary-500 hover:underline">

                            {% if member.description|length > 190 %}
                            <span x-show="!(expanded)">{{ 'Lees meer'|_ }}</span>
                            <span x-show="expanded">{{ 'Lees minder'|_ }}</span>
                            {% endif %}

                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>