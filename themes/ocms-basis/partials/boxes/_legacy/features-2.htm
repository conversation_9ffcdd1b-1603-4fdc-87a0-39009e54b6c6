<section id="features-2" class="features" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        <div class="text-center">
            <h2>{{ box.title }}</h2>
        </div>

        <div class="features-card-grid row-grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for card in box.cards %}
                {% set hideclass = loop.index > 4 ? 'hide-mobile-flex ' %}
                    {% partial "ui/cards/card" cssClass=hideclass ~ "border border-gray-300 rounded md:gap-y-8 group" body %}
                        <div class="card-icon">
                            <div class="relative group-hover:scale-125 transition">
                                <i class="ph-fill {{ card.icon }}"></i>
                            </div>
                        </div>
                        <h3 class="card-title">{{ card.title }}</h3>
                        <div class="card-text content_section">
                            {{ card.content|content }}
                        </div>
                    {% if card.btn_text %}
                        <div class="card-buttons">
                            {% partial 'ui/button' item=card %}
                        </div>
                    {% endif %}
                {% endpartial %}
            {% endfor %}
        </div>
    </div>
</section>
