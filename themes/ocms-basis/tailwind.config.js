const defaultTheme = require('./../ocms-basis/node_modules/tailwindcss/defaultTheme')
const colors = require('./../ocms-basis/node_modules/tailwindcss/colors')
const plugin = require('./../ocms-basis/node_modules/tailwindcss/plugin')

module.exports = {
    darkMode: 'selector',
    content: [
        './assets/js/*.js',
        './../ocms-basis/**/*.htm',
        './../ocms-basis/assets/js/*.js',
        './../ocms-basis-child/layouts/*.htm',
        './../ocms-basis-child/pages/**/*.htm',
        './../ocms-basis-child/partials/**/*.htm',
        '../../plugins/invato/**/*.htm',
        '../../plugins/invato/**/*.js',
        '../../plugins/instalweb/**/*.htm',
        '../../plugins/instalweb/**/*.js',
    ],
    safelist: [
        'oc-boxes-edit-mode',
        'sm:order-3'
    ],
    theme: {
        screens: {
            'xs': '425px',
            'sm': '640px',
            'md': '768px',
            'lg': '1024px',
            'xl': '1280px',
            '2xl': '1536px',
        },
        container: {
            center: true,
            padding: '1rem',
        },
        extend: {
            fontFamily: {
                sans: ['Inter', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                primary: {
                    'highlight': '#bfdbfe', // 200
                    'lighter': '#60a5fa', // 400
                    'darker': '#2563eb', // 600
                    'dark': '#1e40af', // 800
                    'hover': '#2563eb', // 600
                    DEFAULT: '#3b82f6', // 500
                    '50': '#eff6ff',
                    '100': '#dbeafe',
                    '200': '#bfdbfe',
                    '300': '#93c5fd',
                    '400': '#60a5fa',
                    '500': '#3b82f6',
                    '600': '#2563eb',
                    '700': '#1d4ed8',
                    '800': '#1e40af',
                    '900': '#1e3a8a',
                    '950': '#172554',
                },
                secondary: {
                    'highlight': '#fef08a', // 200
                    'lighter': '#facc15', // 400
                    'darker': '#ca8a04', // 600
                    'dark': '#854d0e', // 800
                    'hover': '#ca8a04', // 600
                    DEFAULT: '#eab308', // 500
                    '50': '#fefce8',
                    '100': '#fef9c3',
                    '200': '#fef08a',
                    '300': '#fde047',
                    '400': '#facc15',
                    '500': '#eab308',
                    '600': '#ca8a04',
                    '700': '#a16207',
                    '800': '#854d0e',
                    '900': '#713f12',
                    '950': '#422006',
                },
                gray: colors.gray,
                social: {
                    'facebook': '#3b5999',
                    'messenger': '#0084ff',
                    'twitter': '#55acee',
                    'x': '#000000',
                    'tiktok': '#000000',
                    'github': '#000000',
                    'linkedin': '#0077b5',
                    'dropbox': '#007ee5',
                    'google-plus': '#dd4b39',
                    'pinterest': '#bd081c',
                    'youtube': '#cd201f',
                    'reddit': '#ff5700',
                    'yelp': '#af0606',
                    'producthunt': '#da552f',
                    'soundcloud': '#ff3300',
                    'blogger': '#f57d00',
                    'whatsapp': '#25d366',
                    'instagram': '#e4405f',
                    'dribbble': '#ea4c89',
                    'snapchat': '#fffc00',
                    'spotify': '#1db954',
                }
            },
            animation: {
                'ring': 'ring 1s ease-in-out infinite',
            },
            keyframes: {
                ring: {
                    '0%, 100%': { transform: 'rotate(-6deg)' },
                    '50%': { transform: 'rotate(6deg)' },
                }
            },
            typography: ({theme}) => ({
                DEFAULT: {
                    css: {
                        h1: {fontWeight: '800', marginBottom: '1rem'},
                        h2: {fontWeight: '800', marginBottom: '0.75rem'},
                        h3: {fontWeight: '700', marginBottom: '0.5rem'},
                        h4: {fontWeight: '600', marginBottom: '0.5rem'},
                        'h1 strong': {fontWeight: '800'},
                        'h1 b': {fontWeight: '800'},
                        'h2 strong': {fontWeight: '800'},
                        'h2 b': {fontWeight: '800'},
                        'h3 strong': {fontWeight: '700'},
                        'h3 b': {fontWeight: '700'},
                        'h4 strong': {fontWeight: '700'},
                        'h4 b': {fontWeight: '700'},
                        a: {
                            '&:hover': {
                                color: theme('colors.primary[600]'),
                            },
                        }
                    }
                },
                primary: {
                    css: {
                        '--tw-prose-body': theme('colors.gray[600]'),
                        '--tw-prose-headings': theme('colors.gray[600]'),
                        '--tw-prose-lead': theme('colors.gray[700]'),
                        '--tw-prose-links': theme('colors.primary[500]'),
                        '--tw-prose-bold': theme('colors.gray[700]'),
                        '--tw-prose-counters': theme('colors.primary[600]'),
                        '--tw-prose-bullets': theme('colors.primary[500]'),
                        '--tw-prose-hr': theme('colors.gray[300]'),
                        '--tw-prose-quotes': theme('colors.gray[700]'),
                        '--tw-prose-quote-borders': theme('colors.primary[400]'),
                        '--tw-prose-captions': theme('colors.gray[400]'),
                        '--tw-prose-code': theme('colors.primary[900]'),
                        '--tw-prose-pre-code': theme('colors.gray[100]'),
                        '--tw-prose-pre-bg': theme('colors.gray[800]'),
                        '--tw-prose-th-borders': theme('colors.gray[300]'),
                        '--tw-prose-td-borders': theme('colors.gray[200]'),
                        'h1 strong': {color: theme('colors.primary[600]')},
                        'h2 strong': {color: theme('colors.primary[600]')},
                        'h3 strong': {color: theme('colors.primary[600]')},
                        'h4 strong': {color: theme('colors.primary[700]')},
                        'h1 b': {color: theme('colors.primary[600]')},
                        'h2 b': {color: theme('colors.primary[600]')},
                        'h3 b': {color: theme('colors.primary[600]')},
                        'h4 b': {color: theme('colors.primary[600]')},
                        h1: {color: theme('colors.gray[700]')},
                        h2: {color: theme('colors.gray[700]')},
                        h3: {color: theme('colors.gray[600]')},
                        h4: {color: theme('colors.primary[600]')},
                        a: {
                            '&:hover': {
                                color: theme('colors.primary[600]'),
                            },
                        }
                    },
                },
                primary_inverted: {
                    css: {
                        '--tw-prose-body': theme('colors.gray[100]'),
                        '--tw-prose-headings': theme('colors.gray[100]'),
                        '--tw-prose-lead': theme('colors.white'),
                        '--tw-prose-links': theme('colors.white'),
                        '--tw-prose-bold': theme('colors.white'),
                        '--tw-prose-counters': theme('colors.white'),
                        '--tw-prose-bullets': theme('colors.white'),
                        '--tw-prose-hr': theme('colors.primary[700]'),
                        '--tw-prose-quotes': theme('colors.white'),
                        '--tw-prose-quote-borders': theme('colors.primary[600]'),
                        '--tw-prose-captions': theme('colors.gray[400]'),
                        '--tw-prose-code': theme('colors.white'),
                        '--tw-prose-pre-code': theme('colors.primary[300]'),
                        '--tw-prose-pre-bg': 'rgb(0 0 0 / 25%)',
                        '--tw-prose-th-borders': theme('colors.gray[200]'),
                        '--tw-prose-td-borders': theme('colors.gray[200]'),
                        'h1 strong': {color: theme('colors.primary[600]')},
                        'h2 strong': {color: theme('colors.primary[600]')},
                        'h3 strong': {color: theme('colors.primary[600]')},
                        'h4 strong': {color: theme('colors.primary[600]')},
                        'h1 b': {color: theme('colors.primary[600]')},
                        'h2 b': {color: theme('colors.primary[600]')},
                        'h3 b': {color: theme('colors.primary[600]')},
                        'h4 b': {color: theme('colors.primary[600]')},
                        h1: {color: theme('colors.gray[100]')},
                        h2: {color: theme('colors.gray[100]')},
                        h3: {color: theme('colors.primary[100]')},
                        h4: {color: theme('colors.primary[100]')},
                        a: {
                            '&:hover': {
                                color: theme('colors.primary[600]'),
                            },
                        }
                    },
                },
            }),
            lineClamp: {
                7: '7',
                9: '9',
            },
            fontSize: {
                'xxs': ['11px', '14px'],
            }
        },
        aspectRatio: {
            auto: 'auto',
            square: '1 / 1',
            video: '16 / 9',
            thumb: '4 / 3',
            1: '1',
            2: '2',
            3: '3',
            4: '4',
            5: '5',
            6: '6',
            7: '7',
            8: '8',
            9: '9',
            10: '10',
            11: '11',
            12: '12',
            13: '13',
            14: '14',
            15: '15',
            16: '16',
        },
    },
    plugins: [
        require('./../ocms-basis/node_modules/@tailwindcss/aspect-ratio'),
        require('./../ocms-basis/node_modules/@tailwindcss/typography'),
        require('./../ocms-basis/node_modules/@tailwindcss/forms'),

        plugin(function({ addComponents, theme }) {
            addComponents({
                '.btn-primary': {
                    '&.btn-filled': {
                        backgroundColor: theme('colors.primary[600]'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.primary[500]')
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.primary[600]'),
                        },
                    },
                    '&.btn-outlined': {
                        color: theme('colors.primary[600]'),
                        borderWidth: '1px',
                        borderColor: theme('colors.primary[600]'),
                        '&:hover': {
                            backgroundColor: theme('colors.primary[600]'),
                            color: theme('colors.white'),
                        },
                    },
                    '&.btn-tonal': {
                        backgroundColor: theme('colors.primary[200]'),
                        color: theme('colors.primary[700]'),
                        '&:hover': {
                            backgroundColor: theme('colors.primary[300]'),
                            color: theme('colors.primary[700]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.primary[300]'),
                        },
                    },
                    '&.btn-link': {
                        backgroundColor: 'transparent',
                        color: theme('colors.primary[500]'),
                        boxShadow: 'none',
                        borderRadius: '0',
                        padding: '0',
                        '&:hover': {
                            backgroundColor: 'transparent',
                            color: theme('colors.primary[600]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.primary[300]'),
                        },
                    }
                },
                '.btn-secondary': {
                    '&.btn-filled': {
                        backgroundColor: theme('colors.secondary[500]'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.secondary[600]')
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.secondary[500]'),
                        },
                    },
                    '&.btn-outlined': {
                        color: theme('colors.secondary[500]'),
                        borderWidth: '1px',
                        borderColor: theme('colors.secondary[500]'),
                        '&:hover': {
                            backgroundColor: theme('colors.secondary[500]'),
                            color: theme('colors.white'),
                        },
                    },
                    '&.btn-tonal': {
                        backgroundColor: theme('colors.secondary[200]'),
                        color: theme('colors.secondary[700]'),
                        '&:hover': {
                            backgroundColor: theme('colors.secondary[300]'),
                            color: theme('colors.secondary[700]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.secondary[300]'),
                        },
                    },
                    '&.btn-link': {
                        backgroundColor: 'transparent',
                        color: theme('colors.secondary[500]'),
                        boxShadow: 'none',
                        borderRadius: '0',
                        padding: '0',
                        '&:hover': {
                            backgroundColor: 'transparent',
                            color: theme('colors.secondary[600]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.secondary[300]'),
                        },
                    }
                },
                '.btn-grayscale': {
                    '&.btn-filled': {
                        backgroundColor: theme('colors.gray[500]'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.gray[600]')
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.gray[500]'),
                        },
                    },
                    '&.btn-outlined': {
                        color: theme('colors.gray[500]'),
                        borderWidth: '1px',
                        borderColor: theme('colors.gray[500]'),
                        '&:hover': {
                            backgroundColor: theme('colors.gray[500]'),
                            color: theme('colors.white'),
                        },
                    },
                    '&.btn-tonal': {
                        backgroundColor: theme('colors.gray[200]'),
                        color: theme('colors.gray[700]'),
                        '&:hover': {
                            backgroundColor: theme('colors.gray[300]'),
                            color: theme('colors.gray[700]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.gray[300]'),
                        },
                    },
                    '&.btn-link': {
                        backgroundColor: 'transparent',
                        color: theme('colors.gray[500]'),
                        boxShadow: 'none',
                        borderRadius: '0',
                        padding: '0',
                        '&:hover': {
                            backgroundColor: 'transparent',
                            color: theme('colors.gray[600]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.gray[300]'),
                        },
                    }
                },
                '.btn-error': {
                    '&.btn-filled': {
                        backgroundColor: theme('colors.red[500]'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.red[600]')
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.red[500]'),
                        },
                    },
                    '&.btn-outlined': {
                        color: theme('colors.red[600]'),
                        borderWidth: '1px',
                        borderColor: theme('colors.red[600]'),
                        '&:hover': {
                            backgroundColor: theme('colors.red[600]'),
                            color: theme('colors.white'),
                        },
                    },
                    '&.btn-tonal': {
                        backgroundColor: theme('colors.red[200]'),
                        color: theme('colors.red[700]'),
                        '&:hover': {
                            backgroundColor: theme('colors.red[300]'),
                            color: theme('colors.red[700]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.red[300]'),
                        },
                    },
                    '&.btn-link': {
                        backgroundColor: 'transparent',
                        color: theme('colors.red[500]'),
                        boxShadow: 'none',
                        borderRadius: '0',
                        padding: '0',
                        '&:hover': {
                            backgroundColor: 'transparent',
                            color: theme('colors.red[600]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.red[300]'),
                        },
                    }
                },
                '.btn-info': {
                    '&.btn-filled': {
                        backgroundColor: theme('colors.cyan[500]'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.cyan[600]')
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.cyan[500]'),
                        },
                    },
                    '&.btn-outlined': {
                        color: theme('colors.cyan[600]'),
                        borderWidth: '1px',
                        borderColor: theme('colors.cyan[600]'),
                        '&:hover': {
                            backgroundColor: theme('colors.cyan[600]'),
                            color: theme('colors.white'),
                        },
                    },
                    '&.btn-tonal': {
                        backgroundColor: theme('colors.cyan[200]'),
                        color: theme('colors.cyan[700]'),
                        '&:hover': {
                            backgroundColor: theme('colors.cyan[300]'),
                            color: theme('colors.cyan[700]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.cyan[300]'),
                        },
                    },
                    '&.btn-link': {
                        backgroundColor: 'transparent',
                        color: theme('colors.cyan[500]'),
                        boxShadow: 'none',
                        borderRadius: '0',
                        padding: '0',
                        '&:hover': {
                            backgroundColor: 'transparent',
                            color: theme('colors.cyan[600]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.cyan[300]'),
                        },
                    }
                },
                '.btn-white': {
                    '&.btn-filled': {
                        color: theme('colors.primary[500]'),
                        backgroundColor: theme('colors.white'),
                        '&:hover': {
                            color: theme('colors.primary[600]')
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.white'),
                        },
                    },
                    '&.btn-outlined': {
                        color: theme('colors.white'),
                        borderWidth: '1px',
                        borderColor: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.white'),
                            color: theme('colors.primary[600]'),
                        },
                    },
                    '&.btn-tonal': {
                        backgroundColor: 'rgba(255,255,255,.7)',
                        color: theme('colors.primary[700]'),
                        '&:hover': {
                            backgroundColor: theme('colors.white'),
                            color: theme('colors.primary[700]'),
                        },
                        '&:focus-visible': {
                            outlineColor: 'rgba(255,255,255,.4)',
                        },
                    },
                    '&.btn-link': {
                        backgroundColor: 'transparent',
                        color: theme('colors.white'),
                        boxShadow: 'none',
                        borderRadius: '0',
                        padding: '0',
                        '&:hover': {
                            backgroundColor: 'transparent',
                            color: theme('colors.gray[100]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.gray[100]'),
                        },
                    }
                },
                '.btn-success': {
                    '&.btn-filled': {
                        backgroundColor: theme('colors.green[600]'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.green[500]')
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.green[600]'),
                        },
                    },
                    '&.btn-outlined': {
                        color: theme('colors.green[600]'),
                        borderWidth: '1px',
                        borderColor: theme('colors.green[600]'),
                        '&:hover': {
                            backgroundColor: theme('colors.green[600]'),
                            color: theme('colors.white'),
                        },
                    },
                    '&.btn-tonal': {
                        backgroundColor: theme('colors.green[200]'),
                        color: theme('colors.green[700]'),
                        '&:hover': {
                            backgroundColor: theme('colors.green[300]'),
                            color: theme('colors.green[700]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.green[300]'),
                        },
                    },
                    '&.btn-link': {
                        backgroundColor: 'transparent',
                        color: theme('colors.green[500]'),
                        boxShadow: 'none',
                        borderRadius: '0',
                        padding: '0',
                        '&:hover': {
                            backgroundColor: 'transparent',
                            color: theme('colors.green[600]'),
                        },
                        '&:focus-visible': {
                            outlineColor: theme('colors.green[300]'),
                        },
                    }
                },
            })
        })
    ],
}
