description = "Homepagina layout"

[bannertop]

[bannerbottom]

[popuprender]

[SiteConfig]

[searchInput]
useAutoComplete = 0
autoCompleteResultCount = 5
showProviderBadge = 0
searchPage = "zoeken.htm"

[session]
security = "all"
checkToken = 0

[authentication]
rememberMe = "ask"
twoFactorAuth = 0
recoverPassword = 1
==
<!doctype html>
<html lang="nl">
    <head>
        {% partial "site/head" %}
        {% partial "site/head-child" %}
    </head>
    <body>
        {% partial "site/scripts_body_top" %}

        {% partial 'plugins/notifications/bannertop' %}

        <div class="hero-navbar hero-light hero-wide" data-toggle="sticky-onscroll">
            {% set header = "page/navbars/" ~ this.theme.navbar.selection|default('navbar-1') ~ ".htm" %}
            {% partial header hero="true" %}
        </div>

        <main>
            {% partial 'plugins/notifications/popuprender' %}
            {% page  %}
        </main>

        {% partial 'plugins/notifications/bannerbottom' %}

        {% set footer = "page/footers/" ~ this.theme.footer.selection|default('footer-1') ~ ".htm" %}
        {% partial footer %}

        {% partial "page/cookie-alert" %}

        {% put scripts %}
            <script type="text/javascript" src="{{ 'assets/js/sticky.js' | theme }}"></script>
        {% endput %}

        {% partial "site/foot-child" %}
        {% partial "site/foot" %}

    </body>
</html>
