<div class="flex items-center justify-center p-4 relative {{ item.slug ? 'hover:bg-gray-50' }} transition">
    {% if item.slug %}
        <a href="{{ item.slug | link }}" class="absolute inset-0 z-10" {{ item.target_blank ? 'target="_blank"' }} title="{{ item.title }}"></a>
    {% endif %}

    <img class="max-h-14" src="{{ item.img | media }}" alt="{{ item.title }}">

</div>

{#
    accepted variables:

    'item'
        - item reference
        - type: object
        - required: no

#}
