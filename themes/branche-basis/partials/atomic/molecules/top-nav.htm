[staticMenu topMenu]
code = "menu-top"
==
{% set topmenuItems = topMenu.resetMenu(this.theme.top_menu) %}
<nav id="top-nav">
    <ul class="flex flex-wrap space-x-8">
        {% for item in topmenuItems %}
            {% if not item.viewBag.isHidden %}
                <li role="presentation" class="{{ item.isActive ? 'active' : '' }} {{ item.isChildActive ? 'child-active' : '' }} {{ item.viewBag.cssClass }}">
                    {% if item.url %}
                        {% if item.viewBag.cssClass == 'highlight' %}
                            <a href="{{ item.url }}" class="leading-none font-semibold" {{ item.viewBag.isExternal ? 'target="_blank"' }}>
                                {{ item.title }}
                            </a>
                        {% else %}
                            <a href="{{ item.url }}" class="leading-none font-semibold {{ item.isActive ? 'opacity-100' : 'opacity-90' }} hover:opacity-100" {{ item.viewBag.isExternal ? 'target="_blank"' }}>
                                {{ item.title }}
                            </a>
                        {% endif %}
                    {% endif %}
                </li>
            {% endif %}
        {% endfor %}

    </ul>
</nav>
