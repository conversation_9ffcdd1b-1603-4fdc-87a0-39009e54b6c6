<ul class="hidden xl:flex items-center space-x-12">

    {% for item in mainmenuItems %}
        <li class="relative group {{ item.isActive or item.isChildActive ? 'text-primary-800 hover:text-gray-800' : 'text-gray-800 hover:text-primary-800' }}">

            {% if item.type == 'header' %}
            <p>{{ item.title }} {% if item.items %}<i class="fa-regular fa-sm fa-chevron-down dropdown-icon ml-1"></i>{% endif %}</p>
            {% else %}
            <a href="{{ item.url }}" class="text-lg font-semibold capitalize">{{ item.title }} {% if item.items %}<i class="fa-regular fa-sm fa-chevron-down dropdown-icon ml-1"></i>{% endif %}</a>
            {% endif %}

            {% if item.items %}
            <ul class="hidden group-hover:block bg-white absolute w-64 -translate-x-4 left-[2px] shadow-xl z-50">
                {% for subitem in item.items %}
                <li>
                    <a href="{{ subitem.url }}" class="flex flex-wrap items-center relative capitalize text-lg font-semibold pl-4 py-2 whitespace-no-wrap {{ subitem.isActive ? 'text-primary-800 hover:text-gray-800' : 'text-gray-800 hover:text-primary-800' }}">
                        {{ subitem.title }}
                    </a>
                </li>
                {% endfor %}
            </ul>
            {% endif %}
        </li>
    {% endfor %}
</ul>