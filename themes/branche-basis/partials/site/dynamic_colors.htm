<script>
    {% if this.theme.primary_500 %}
        var primary_default = chroma('{{ this.theme.primary_default }}').rgb();
        var primary_50 = chroma('{{ this.theme.primary_50 }}').rgb();
        var primary_100 = chroma('{{ this.theme.primary_100 }}').rgb();
        var primary_200 = chroma('{{ this.theme.primary_200 }}').rgb();
        var primary_300 = chroma('{{ this.theme.primary_300 }}').rgb();
        var primary_400 = chroma('{{ this.theme.primary_400 }}').rgb();
        var primary_500 = chroma('{{ this.theme.primary_500 }}').rgb();
        var primary_600 = chroma('{{ this.theme.primary_600 }}').rgb();
        var primary_700 = chroma('{{ this.theme.primary_700 }}').rgb();
        var primary_800 = chroma('{{ this.theme.primary_800 }}').rgb();
        var primary_900 = chroma('{{ this.theme.primary_900 }}').rgb();
        var primary_950 = chroma('{{ this.theme.primary_950 }}').rgb();
        var primary_900 = chroma('{{ this.theme.primary_100 }}').desaturate(0.1).darken(1.7).rgb();
    {% else %}
        var primary_default = chroma('#5b21b6').rgb();
        var primary_50 = chroma('#f5f3ff').rgb();
        var primary_100 = chroma('#ede9fe').rgb();
        var primary_200 = chroma('#ddd6fe').rgb();
        var primary_300 = chroma('#c4b5fd').rgb();
        var primary_400 = chroma('#a78bfa').rgb();
        var primary_500 = chroma('#8b5cf6').rgb();
        var primary_600 = chroma('#7c3aed').rgb();
        var primary_700 = chroma('#6d28d9').rgb();
        var primary_800 = chroma('#5b21b6').rgb();
        var primary_900 = chroma('#4c1d95').rgb();
        var primary_950 = chroma('#2e1065').rgb();
    {% endif %}

    {% if this.theme.secondary_500 %}
        var secondary_default = chroma('{{ this.theme.secondary_default }}').rgb();
        var secondary_50 = chroma('{{ this.theme.secondary_50 }}').rgb();
        var secondary_100 = chroma('{{ this.theme.secondary_100 }}').rgb();
        var secondary_200 = chroma('{{ this.theme.secondary_200 }}').rgb();
        var secondary_300 = chroma('{{ this.theme.secondary_300 }}').rgb();
        var secondary_400 = chroma('{{ this.theme.secondary_400 }}').rgb();
        var secondary_500 = chroma('{{ this.theme.secondary_500 }}').rgb();
        var secondary_600 = chroma('{{ this.theme.secondary_600 }}').rgb();
        var secondary_700 = chroma('{{ this.theme.secondary_700 }}').rgb();
        var secondary_800 = chroma('{{ this.theme.secondary_800 }}').rgb();
        var secondary_900 = chroma('{{ this.theme.secondary_900 }}').rgb();
        var secondary_950 = chroma('{{ this.theme.secondary_950 }}').rgb();
    {% else %}
        var secondary_default = chroma('#f59e0b').rgb();
        var secondary_50 = chroma('#fffbeb').rgb();
        var secondary_100 = chroma('#fef3c7').rgb();
        var secondary_200 = chroma('#fde68a').rgb();
        var secondary_300 = chroma('#fcd34d').rgb();
        var secondary_400 = chroma('#fbbf24').rgb();
        var secondary_500 = chroma('#f59e0b').rgb();
        var secondary_600 = chroma('#d97706').rgb();
        var secondary_700 = chroma('#b45309').rgb();
        var secondary_800 = chroma('#92400e').rgb();
        var secondary_900 = chroma('#78350f').rgb();
        var secondary_950 = chroma('#451a03').rgb();
    {% endif %}

    var style = document.createElement("style");
    style.innerHTML = ":root {";
    style.innerHTML = style.innerHTML + "--color-primary-default: "+ primary_default +";";
    style.innerHTML = style.innerHTML + "--color-primary-50: "+ primary_50 +";";
    style.innerHTML = style.innerHTML + "--color-primary-100: "+ primary_100 +";";
    style.innerHTML = style.innerHTML + "--color-primary-200: "+ primary_200 +";";
    style.innerHTML = style.innerHTML + "--color-primary-300: "+ primary_300 +";";
    style.innerHTML = style.innerHTML + "--color-primary-400: "+ primary_400 +";";
    style.innerHTML = style.innerHTML + "--color-primary-500: "+ primary_500 +";";
    style.innerHTML = style.innerHTML + "--color-primary-600: "+ primary_600 +";";
    style.innerHTML = style.innerHTML + "--color-primary-700: "+ primary_700 +";";
    style.innerHTML = style.innerHTML + "--color-primary-800: "+ primary_800 +";";
    style.innerHTML = style.innerHTML + "--color-primary-900: "+ primary_900 +";";
    style.innerHTML = style.innerHTML + "--color-primary-950: "+ primary_950 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-default: "+ secondary_default +";";
    style.innerHTML = style.innerHTML + "--color-secondary-50: "+ secondary_50 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-100: "+ secondary_100 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-200: "+ secondary_200 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-300: "+ secondary_300 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-400: "+ secondary_400 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-500: "+ secondary_500 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-600: "+ secondary_600 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-700: "+ secondary_700 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-800: "+ secondary_800 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-900: "+ secondary_900 +";";
    style.innerHTML = style.innerHTML + "--color-secondary-950: "+ secondary_950 +";";
    style.innerHTML = style.innerHTML + "}";
    document.head.appendChild(style);

</script>
