<?php

namespace Invato\Database\Seeders;

use Backend\Models\UserRole;
use File;
use Illuminate\Database\Seeder;
use JsonException;
use October\Rain\Support\Facades\Schema;

class SiteManagerRoleSeeder extends Seeder
{
    /**
     * @throws JsonException
     */
    public function run(): void
    {
        $path = __DIR__.'/data/site-manager-roles.json';
        $data = json_decode(File::get($path), true, 512, JSON_THROW_ON_ERROR);

        if (Schema::hasTable('backend_user_roles')) {
            foreach ($data as $item) {
                UserRole::updateOrCreate([
                    'name' => $item['name'],
                ], $item);
            }
        }
    }
}
