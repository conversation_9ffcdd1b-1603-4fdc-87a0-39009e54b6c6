//
// Breadcrumb
// --------------------------------------------------

@color-breadcrumb-text:           var(--bs-link-color);
@color-breadcrumb-active-text:    var(--bs-secondary-color);

.control-breadcrumb {
    ul {
        padding: 0 0 20px 0;
        margin: 0;
        font-size: 0;
    }

    ul li {
        font-size: @font-size-base;
        list-style: none;
        margin: 0;
        padding:  0;
        display: inline-block;
        position: relative;
        color: @color-breadcrumb-text;

        a {
            display: inline-block;
            color: @color-breadcrumb-text;
            text-decoration: underline;
            &:hover { color: @color-breadcrumb-text; }
        }

        &:after {
            content: var(--bs-breadcrumb-divider, "/");
            position: relative;
            display: inline-block;
            margin: 0 5px;
            color: @color-breadcrumb-active-text;
        }

        &:last-child {
            background-color: transparent;
            color: @color-breadcrumb-active-text;

            &:after {
                display: none;
            }
        }
    }
}

// Breadcrumb to sit flush to the element below
body.breadcrumb-flush .control-breadcrumb,
.control-breadcrumb.breadcrumb-flush {
    margin-bottom: 0;
}

body.compact-container {
    .control-breadcrumb {
        margin-top: 20px;
        margin-left: 20px;
        margin-right: 20px;

        > ul {
            padding-bottom: 0;
        }
    }
}

body.slim-container {
    .control-breadcrumb {
        margin-left: 20px;
        margin-right: 20px;
    }
}
