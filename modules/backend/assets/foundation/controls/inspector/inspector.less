//
// Inspector
// --------------------------------------------------

@color-inspector-bg:                         @input-bg;
@color-inspector-color:                      @primary-color;
@color-inspector-border:                     @primary-border;
@color-inspector-label-bg:                   @input-bg;
@color-inspector-value-bg:                   @input-bg;
@color-inspector-value-color:                @input-color;
@color-inspector-active-bg:                  @input-bg;
@color-inspector-changed:                    #c03f31;

//
// Inspector
// --------------------------------------------------

.inspector-autocomplete-list() {
    background: @dropdown-bg;
    font-size: 12px;
    z-index: @zindex-inspector;

    li a {
        padding: 5px 12px;
        white-space: normal;
        word-wrap: break-word;
    }
}

.inspector-fields {
    min-width: 220px;
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed;
    .border-bottom-radius(2px);

    td, th {
        padding: 5px 12px;
        font-size: 12px;
        border-bottom: 1px solid @color-inspector-border;
        text-align: left;
    }

    th {
        color: @color-inspector-color;
        width: 45%;
    }

    td {
        color: @color-inspector-value-color;
        width: 55%;
    }

    tr:last-child {
        td, th { border-bottom: none; }

        td {
            &, input[type=text] {
                border-radius: 0 0 2px 0;
            }
        }
    }

    tr.group {
        user-select: none;

        th {
            // background: @tertiary-bg;
            font-weight: 600;
            cursor: pointer;
        }
    }

    tr.invalid th {
        color: #c03f31!important;
    }

    tr.control-group {
        user-select: none;

        th, td {
            cursor: pointer;
        }
    }

    tr {
        &.collapsed { display: none; }
        &.expanded { display: table-row; }
    }

    &.has-groups {
        th {
            padding-left: 20px;
        }

        tr.grouped th {
            padding-left: 35px;
        }
    }

    &:not(:hover) td {
        border-left-color: transparent;
    }

    th {
        background: @color-inspector-label-bg;
    }

    td {
        font-weight: 400;
        border-left: 1px solid @color-inspector-border;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        background: @color-inspector-value-bg;
        transition: border-left-color 0.35s;

        &.text {
            input[type=text] {
                background: @color-inspector-value-bg;
                color: @color-inspector-value-color;
                text-overflow: ellipsis;

                &::placeholder {
                    font-weight: normal!important;
                    color: #b5babd;
                }
            }

            &.active {
                background: @color-inspector-active-bg;
            }
        }

        &.autocomplete {
            padding: 0;
            position: relative;
            overflow: visible;

            .autocomplete-container {
                input[type=text] {
                    padding: 5px 12px;
                }

                ul.dropdown-menu {
                    .inspector-autocomplete-list();
                }

                .loading-indicator {
                    span {
                        margin-top: -12px;
                        right: 10px;
                        left: auto;
                    }
                }
            }
        }

        &.trigger-cell {
            padding: 0!important;

            a.trigger {
                display: block;
                padding: 5px 12px 7px 12px;
                overflow: hidden;
                min-height: 29px;
                text-overflow: ellipsis;
                color: @color-inspector-color;
                text-decoration: none;

                &.cell-placeholder {
                    color: #b5babd;
                }

                .loading-indicator {
                    background-color: @color-inspector-bg;

                    span {
                        margin-top: -12px;
                        right: 10px;
                        left: auto;
                    }
                }
            }
        }

        &.dropdown {
            padding: 0!important;
        }

        select {
            width: 90%;
        }

        div.external-param-editor-container {
            position: relative;
            padding-right: 25px;

            div.external-editor {
                bottom: 0;
                margin: -5px -12px;
                right: 30px;
                left: auto;
                top: 0;
                position: absolute;

                transition: left 0.2s;
                transform: translateZ(0);
                will-change: transform;

                div.controls {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    top: 0;

                    a {
                        position: absolute;
                        left: 0;
                        top: 0;
                        display: inline-block;
                        height: 100%;
                        width: 30px;
                        color: @primary-color;
                        outline: none;

                        i {
                            display: inline-block;
                            position: relative;
                            left: 7px;
                            top: 5px;
                            font-size: 1rem;
                        }
                    }

                    input {
                        position: absolute;
                        display: block;
                        border: none;
                        width: 100%;
                        height: 100%;
                        left: 0;
                        top: 0;
                        padding-left: 30px;
                        padding-right: 12px;
                        background: transparent;
                        color: @color-inspector-value-color;
                    }
                }
            }

            &.editor-visible {
                div.external-editor {
                    div.controls {
                        input {
                            background: @color-inspector-bg;
                        }
                    }
                }
            }
        }

        &.active {
            div.external-param-editor-container {
               div.external-editor {
                    div.controls {
                        input {
                            background: @secondary-bg;
                        }
                    }
                }
            }
        }

        &.dropdown, &.trigger-cell {
            div.external-param-editor-container div.external-editor {
                height: 100%;
                margin: 0;
                bottom: auto;
            }
        }
    }

    th {
        font-weight: 600;

        > div {
            position: relative;

            > div {
                white-space: nowrap;
                padding-right: 10px;
                text-overflow: ellipsis;
                overflow: hidden;
                width: 100%;

                span.info {
                    display: inline-block;
                    position: absolute;
                    right: 3px;
                    top: 3px;
                    font-size: 14px;
                    width: 10px;
                    height: 12px;
                    line-height: 80%;
                    color: #999;
                    &:before {
                        margin-left: 3px;
                    }
                    &:hover {
                        color: @emphasis-color;
                    }
                }
            }

            a.expandControl {
                display: block;
                position: absolute;
                width: 12px;
                height: 12px;
                left: -15px;
                top: 2px;
                text-indent: -100000em;

                span {
                    position: absolute;
                    display: inline-block;
                    left: 0;
                    top: 0;
                    width: 12px;
                    height: 12px;

                    &:after {
                        .icon(@icon-angle-right);
                        position: absolute;
                        left: 4px;
                        top: -2px;
                        width: 12px;
                        height: 12px;
                        font-size: 13px;
                        color: @primary-color;
                        text-indent: 0;
                    }
                }

                &.expanded span:after {
                    .icon(@icon-angle-down);
                    left: 2px;
                }
            }
        }
    }

    input[type=text] {
        display: block;
        width: 100%;
        border: none;
        outline: none;
    }

    div.form-check {
        position: relative;
        top: 1px;
        font-size: 1rem;
    }

    .select2-container {
        width: 100% !important;

        .select2-selection {
            height: 29px;
            line-height: 29px;
            padding: 0 3px 0 12px;
            border: none !important;
            font-size: 12px;
            .border-radius(0) !important;
            .box-shadow(none) !important;

            &.select2-default {
                font-weight: normal !important;
            }
        }

        .loading-indicator {
            > span {
                top: 15px;
            }
        }

        &.select2-container--open {
            .border-radius(0) !important;
            border: none !important;

            .select2-selection {}
        }

        .select2-selection__rendered {
            padding: 0 22px 0 0;
            color: @color-inspector-value-color;
        }
    }

    tr.changed {
        td {
            font-weight: 600;
            input[type=text] {
                font-weight: 600;
            }

            .select2-container .select2-selection {
                font-weight: 600;
            }
        }
    }

}

div.control-popover {
    &.control-inspector {
        > div {
            background: @color-inspector-bg;
            border: none;

            &:before, &:after {
                display: none;
            }
        }

        > div > form {
            padding: 5px 20px 10px;
        }

        .popover-head {
            padding-left: 20px;
            padding-right: 20px;
        }

        .inspector-fields {
            th {
                padding-left: 0;
            }
        }
    }

    &.hero {
        > div {
            border-radius: 8px;
        }

        .popover-head {
            .border-top-radius(8px);

            h3 {
                font-weight: normal;
                font-size: 18px;
            }

            .btn-close {
                top: 16px;
                right: 17px;
            }
        }

        .inspector-fields {
            th, td {
                padding: 9px 12px;
                font-weight: 600!important;
                font-size: 13px;
            }

            th {
                padding-left: 0;
            }

            td {
                font-weight: 400!important;
            }

            div.custom-select.select2-container .select2-selection {
                height: 36px;
                line-height: 36px;
            }
        }
    }

    &.inspector-temporary-placement {
        visibility: hidden;
        left: 0!important;
        top: 0!important;
    }
}

.inspector-columns-editor {
    min-height: 400px;
    margin-bottom: 20px;
    border-bottom: 1px solid @color-inspector-border;

    .items-column {
        width: 250px;
    }

    .inspector-wrapper {
        background: @color-inspector-bg;
        border-left: 2px solid @color-inspector-border;
    }

    .toolbar {
        padding: 20px;
    }
}

html.gecko.mac {
    .scroll-wrapper.inspector-wrapper > div {
        margin-right: 17px;
    }
}

.inspector-table-list {
    border-top: 1px solid @border-color;
    user-select: none;
}

div.inspector-dictionary-container {
    border: 1px solid @border-color;

    .values {
        height: 300px;
    }

    table.headers {
        width: 100%;
        border: none;

        td {
            width: 50%;
            padding: 7px 5px;
            font-size: 13px;
            text-transform: uppercase;
            font-weight: 600;
            color: @primary-color;
            background: @primary-bg;
            border-bottom: 1px solid @border-color;

            &:first-child {
                border-right: 1px solid @border-color;
            }
        }
    }

    table.inspector-dictionary-table {
        width: 100%;
        border: none;

        tbody tr {
            td {
                width: 50%;
                padding: 0!important;
                border-bottom: 1px solid @border-color;

                div {
                    border: 1px solid transparent;
                }

                &.active div {
                    border-color: @brand-accent;
                }

                input {
                    width: 100%;
                    height: 100%;
                    display: block;
                    outline: none;
                    border: none;
                    padding: 7px 5px;
                    box-shadow: none;

                    &:focus {
                        border: none;
                        outline: none;
                    }
                }

                &:first-child {
                    border-right: 1px solid @border-color;
                }
            }

            &:last-child td {
                border-bottom: none;
            }
        }
    }
}

.inspector-header {
    background: @color-popover-head-bg;
    padding: 14px 16px;
    position: relative;
    color: @color-popover-head-text;
    border-bottom: 1px solid @color-popover-border;

    h3 {
        font-size: @font-size-base + 1;
        font-weight: 600;
        margin-top: 0;
        margin-bottom: 0;
        padding-right: 15px;
        line-height: 130%;
    }

    p {
        font-size: @font-size-base - 2;
        font-weight: normal;
        margin: 5px 0 0 0;

        &:empty {
            display: none;
        }
    }

    span, a {
        text-decoration: none;
        position: absolute;
        top: 12px;
        float: none;
        color: @close-color;
        cursor: pointer;
        line-height: 1;

        opacity: .4;
        &:hover {
            opacity: 1;
            color: @close-color;
        }
    }

    .detach {
        right: 26px;
        line-height: 22px;
    }

    .close {
        right: 11px;
        font-size: 21px;
    }
}

.inspector-container {
    &:empty {
        display: none;
    }

    .control-scrollpad {
        position: absolute;
    }
}

.inspector-field-comment {
    &:empty {
        display: none;
    }
}

ul.autocomplete.dropdown-menu.inspector-autocomplete {
    .inspector-autocomplete-list();
}

.select2-dropdown {
    &.ocInspectorDropdown {
        font-size: 12px;
        .border-radius(0) !important;
        border: none !important;

        > .select2-results {
            > .select2-results__options {
                font-size: 12px;
            }
            > li > div {
                padding: 5px 12px 5px;
            }

            li.select2-no-results {
                padding: 5px 12px 5px;
            }

            li > i, li > img {
                margin-left: 6px;
            }
        }

        .select2-search {
            min-height: 26px;
            position: relative;
            border-bottom: 1px solid @border-color;

            &:after {
                position: absolute;
                .icon(@icon-search);
                right: 10px;
                top: 10px;
                color: #95a5a6;
            }

            input.select2-search__field {
                min-height: 26px;
                background: transparent !important;
                font-size: @font-size-base - 1;
                padding-left: 4px;
                padding-right: 20px;
                border: none;
            }
        }
    }
}
