// Nicer variables
:root {
    --oc-brand-primary:          var(--bs-primary);
    --oc-brand-secondary:        var(--bs-secondary);
    --oc-brand-success:          var(--bs-success);
    --oc-brand-info:             var(--bs-info);
    --oc-brand-warning:          var(--bs-warning);
    --oc-brand-danger:           var(--bs-danger);
    --oc-brand-light:            var(--bs-light);
    --oc-brand-dark:             var(--bs-dark);

    --oc-body-bg:                var(--bs-body-bg);
    --oc-text-color:             var(--bs-body-color);
    --oc-text-muted:             var(--bs-secondary-color);
    --oc-link-color:             var(--bs-link-color);
    --oc-link-hover-color:       var(--bs-link-hover-color);
    --oc-heading-color:          var(--bs-heading-color);
    --oc-emphasis-color:         var(--bs-emphasis-color);
    --oc-border-color:           var(--bs-border-color);
    --oc-popup-bg:               var(--bs-modal-bg);
    --oc-backdrop-opacity:       var(--bs-backdrop-opacity);
}

// Defining RGB
// Light Mode Notes:
// - To produce rbg in LESS: @rgbColor: red(@hexColor), green(@hexColor), blue(@hexColor);
:root, [data-bs-theme="light"] {
    // --bs-border-color: #d7e1ea;
    // --oc-border-focus: #72809d;
    --bs-emphasis-color: #333333;
    --bs-border-color: #c4ced7;
    --bs-backdrop-opacity: 0.2;
    --oc-border-focus: var(--oc-primary-active-bg);
    --oc-highlight-color: white;

    //
    // Primary Colors
    // --------------------------------------------------

    --bs-success: var(--bs-green);
    --bs-info: var(--bs-blue);
    --bs-warning: var(--bs-yellow);
    --bs-danger: var(--bs-red);

    --oc-accent: #3498db;
    --oc-accent-text: #258cd1; //darker 5%
    --oc-selection: var(--bs-teal);
    --oc-selection-rgb: 107, 196, 141;
    --oc-selection-text: #39905a; //darken 20%

    --oc-primary-bg: white;
    --oc-primary-color: #5e6d8c;
    --oc-primary-border: #cfd7e1;
    --oc-primary-hover-bg: tint(#6a6cf7, 15%);
    --oc-primary-active-bg: tint(#6a6cf7, 20%);

    --bs-secondary: #72809d;
    --bs-secondary-color: #72809d;
    --oc-secondary-bg: #d7e1ea;
    --oc-secondary-hover-bg: darken(#eeeff0, 5%);
    --oc-secondary-active-bg: darken(#eeeff0, 10%);

    //
    // Form Variables
    // --------------------------------------------------

    --oc-form-control-bg: white;
    --oc-form-control-disabled-bg: #e9ecef;
    --oc-form-control-disabled-color: #899c9d;
    --oc-input-translatable-color: #75809b;
    --oc-input-translatable-bg: #ebf0fb;
    --oc-input-selection-color: #000;
    --oc-input-selection-bg: #b5d6fd;

    //
    // Component Variables
    // --------------------------------------------------

    --oc-mainnav-color: white;
    --oc-mainnav-bg: #2D3134;
    --oc-mainnav-icon-color: white;

    --oc-sidebar-color: #536061;
    --oc-sidebar-bg: #e9edf3;
    --oc-sidebar-active-color: #333;
    --oc-sidebar-active-bg: white;
    --oc-sidebar-active-border: var(--bs-primary);
    --oc-sidebar-hover-bg: white;

    --oc-editor-bg: #f0f4f8;
    --oc-editor-section-color: #333;
    --oc-editor-section-bg: #d7e1eA;
    --oc-editor-tab-color: #5e6d8c;
    --oc-editor-tab-bg: #e9edf3;
    --oc-editor-tab-active-color: #2c3e4f;
    --oc-editor-tab-active-bg: var(--bs-body-bg);

    --oc-document-toolbar-bg: var(--bs-body-bg);
    --oc-document-tabs-bg: white;
    --oc-document-content-bg: white;
    --oc-document-ruler-bg: #d7e1ea;
    --oc-document-ruler-color: white;
    --oc-document-ruler-tick: #bdc3c7;

    --oc-settings-color: #536061;
    --oc-settings-bg: #f0f4f8;
    --oc-settings-item: white;
    --oc-settings-active-color: white;
    --oc-settings-active-bg: #6bc48d;
    --oc-settings-hover-bg: #dfe7ee;

    --oc-toolbar-color: #536061;
    --oc-toolbar-bg: white;
    // --oc-toolbar-border: #ecf0f1;
    --oc-toolbar-border: #d7e1ea;
    --oc-toolbar-hover-color: black;
    --oc-toolbar-hover-bg: rgba(215,225,234,0.7);

    --oc-tab-color: #72809d;
    --oc-tab-bg: #ffffff;
    --oc-tab-active-color: #35425b;
    --oc-tab-border: #d7e1eA;
    --oc-tab-hover-bg: rgba(215,225,234,0.7);

    --oc-dropdown-trigger-border: #bcc3c7;
    --oc-dropdown-trigger-color: #536061;
    --oc-dropdown-trigger-bg: white;
    --oc-dropdown-hover-bg: var(--bs-primary);
    --oc-dropdown-hover-color: white;
    --oc-dropdown-active-bg: rgba(var(--bs-primary-rgb), .95);
    --oc-dropdown-active-color: white;

    &,.table {
        --bs-table-color: #536061;
        --bs-table-bg: white;
        --bs-table-striped-bg: #f7f9fb;
        // --bs-table-border-color: #ecf0f1;
        --bs-table-border-color: #d7e1ea;
        --bs-table-hover-bg: #fff5cb;
        --oc-table-active-bg: darken(#fff5cb, 5%);
    }

    &,.pagination {
        --bs-pagination-active-bg: var(--bs-primary);
        --bs-pagination-active-border-color: var(--bs-primary);
    }

    &,.breadcrumb {
        --bs-breadcrumb-item-padding-x: 0.4rem;
    }

    &,.modal {
        --bs-modal-bg: white;
    }
}

// Dark Mode Notes:
// - avoid using "white" for text, use e0e0e0 instead
//
[data-bs-theme="dark"] {
    --bs-heading-color: #e0e0e0;
    --bs-emphasis-color: white;
    --bs-border-color: #383a3e;
    --bs-backdrop-opacity: 0.5;
    --oc-highlight-color: black;

    //
    // Primary Colors
    // --------------------------------------------------

    --bs-blue:    #0d6efd;
    --bs-indigo:  #6610f2;
    --bs-purple:  #6f42c1;
    --bs-pink:    #d63384;
    --bs-red:     #dc3545;
    --bs-orange:  #fd7e14;
    --bs-yellow:  #ffc107;
    --bs-green:   #41b862;
    --bs-teal:    #24b58a;
    --bs-cyan:    #0dcaf0;

    --oc-primary-color: rgba(173, 181, 189, 0.85);
    --oc-primary-bg: #141515;
    --oc-primary-border: #494c50;

    --bs-secondary-color: rgba(173, 181, 189, 0.75);
    --oc-secondary-bg: darken(#888, 10%);
    --oc-secondary-hover-bg: darken(#888, 5%);
    --oc-secondary-active-bg: #888;

    //
    // Form Variables
    // --------------------------------------------------

    --oc-form-control-bg: #1e2227;
    --oc-form-control-disabled-bg: #343a40;
    --oc-input-translatable-color: rgba(173, 181, 189, 0.85);
    --oc-input-translatable-bg: #272b2e;
    --oc-input-selection-color: #e0e0e0;
    --oc-input-selection-bg: #373f47;

    //
    // Component Variables
    // --------------------------------------------------

    --oc-sidebar-color: #d7e1eA;
    --oc-sidebar-bg: #292a2d;
    --oc-sidebar-active-color: white;
    --oc-sidebar-active-bg: #424242;
    --oc-sidebar-hover-bg: #424242;

    --oc-editor-bg: #212529;
    --oc-editor-section-bg: #383a3e;
    --oc-editor-section-color: #e0e0e0;
    --oc-editor-tab-color: #adb5bd;
    --oc-editor-tab-bg: #181a1e;
    --oc-editor-tab-active-color: #e0e0e0;
    --oc-editor-tab-active-bg: #202124;

    --oc-document-toolbar-bg: #202124;
    --oc-document-tabs-bg: #24262a;
    --oc-document-content-bg: #181a1e;
    --oc-document-ruler-bg: #353b42;
    --oc-document-ruler-color: #1e2227;
    --oc-document-ruler-tick: #495057;

    --oc-settings-color: #adb5bd;
    --oc-settings-bg: #1b1f22;
    --oc-settings-item: #212529;
    --oc-settings-active-color: white;
    --oc-settings-active-bg: #2b3442;
    --oc-settings-hover-bg: #2b3442;

    --oc-toolbar-color: #adb5bd;
    --oc-toolbar-bg: #1e2227;
    --oc-toolbar-border: #242a30;
    --oc-toolbar-hover-color: white;
    --oc-toolbar-hover-bg: #43484e; //rgba(215,225,234,0.2);

    --oc-tab-color: var(--bs-body-color);
    --oc-tab-bg: var(--bs-body-bg);
    --oc-tab-active-color: #e0e0e0;
    --oc-tab-border: #383a3e;
    --oc-tab-hover-bg: rgba(215,225,234,0.2);

    --oc-dropdown-trigger-border: #495057;
    --oc-dropdown-trigger-color: #e0e0e0;
    --oc-dropdown-trigger-bg: #12262c;

    &,.table {
        --bs-table-color: #adb5bd;
        --bs-table-bg: #1e2227;
        --bs-table-striped-bg: rgba(0,0,0,0.05);
        --bs-table-border-color: #242a30;
        --bs-table-hover-bg: #35425b;
        --bs-table-hover-color: #e0e0e0;
        --oc-table-active-bg: darken(#35425b, 5%);
    }

    &,.modal {
        --bs-modal-bg: #1a1d21;
    }
}
