//== Tables
//
//## Customizes the `.table` component with basic values, each used across all table variations.

@table-cell-padding:            8px;
@table-condensed-cell-padding:  5px;

@table-bg:                      transparent;
@table-bg-accent:               #f9f9f9;
@table-bg-hover:                #f5f5f5;
@table-bg-active:               #FFF5CB;

@table-border-color:            #ddd;

@color-list-active:             #dddddd;
@color-list-hover:              #dddddd;
@color-list-active-border:      @brand-primary;
@color-list-arrow:              #cfcfcf;
@color-list-icon:               #a1aab1;
@color-list-parent-bg:          #ffffff;
@color-list-nav-arrow:          #34495e;
@color-list-head-bg:            @color-grey-4;
@color-list-progress-bg:        #0181b9;
@color-list-border:             #D4D8DA;
@color-list-row-border:         #ECF0F1;
@color-list-head-border:        @primary-border;
@color-list-border-light:       @color-grey-3;
@color-list-text-head:          @color-grey-1;
@color-list-text:               var(--bs-table-color);
@color-list-text-active:        #000000;
@color-list-text-tree:          var(--bs-table-color);
@color-list-stripe-active:      @brand-primary;
@color-list-accent:             #F7F9FB;
@color-list-norecords-text:     @secondary-color;
@color-list-hover-bg:           @background-color-focus;
@color-list-hover-bg-active:    darken(@table-bg-active, 5%);
@color-list-hover-text:         @color-list-text-active;
@color-list-active-bg:          @color-grey-3;
@color-list-active-text:        @color-list-text-active;
@color-list-active-sort:        #c63e26;
@color-list-grid:               #E4E7E8;
@color-list-container-bg:       #ffffff;

@color-status-list-text:        #7e8c8d;
