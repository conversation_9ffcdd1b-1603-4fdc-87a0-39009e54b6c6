@import "../../../../assets/less/core/boot.less";

@import "_resizing.less";

.modal.component-backend-modal {
    &.fade {
        &:not(.show) {
            pointer-events: none;
        }

        .modal-dialog {
            opacity: 0;
            transition: all 0.15s, width 0s;
            transform: scale(0.95);
        }
    }

    &.fade.show {
        .modal-dialog {
            opacity: 1;
            transform: scale(1);
        }
    }
}

.modal.component-backend-modal {
    display: block;

    &.non-modal {
        overflow: visible;
        overflow-y: visible;
        right: auto;
        bottom: auto;
        left: 0;
        top: 0;
        width: 0;
        height: 0;

        .modal-dialog {
            width: auto;
            margin: 0;
        }
    }

    &.temporary-hidden {
        display: none;
    }

    .modal-content {
        border: @popup-border;
        border-radius: 8px;
        box-shadow: @box-shadow-z2;
        background-color: @popup-bg;
        overflow: visible;
        word-break: break-word;

        .modal-header {
            border-bottom: @popup-border;
            padding: 15px 20px;

            .close {
                width: 19px;
                height: 19px;
                border-radius: 20px;
                position: relative;
                top: 5px;
                .opacity(1);
                .backend-hide-text();

                &:after {
                    display: none!important;
                }

                &[disabled] {
                    .opacity(0.5);
                    cursor: default;
                }

                &:focus {
                    outline: none;
                    background-color: @toolbar-focus-bg;
                }

                &:before {
                    display: block;
                    width: 11px;
                    height: 11px;
                    position: absolute;
                    background-position: -253px 0;
                    top: 4px;
                    right: 4px;
                }

                &:not([disabled]):focus,
                &:not([disabled]):hover {
                    &:before {
                        background-position: -268px 0;
                    }
                }
            }

            h4 + p {
                margin-top: 10px;
                margin-bottom: 0;
            }
        }

        &.draggable .modal-header {
            user-select: none;
        }

        &.animate-position {
            transition: left .2s, top .2s;
        }

        .modal-body {
            padding-top: 15px;
        }

        .modal-footer {
            text-align: left;
        }
    }
}

@media (max-width: @screen-sm) {
    .modal.component-backend-modal:not(.non-modal) .modal-content {
        width: auto !important;
        height: auto !important;
        left: 0 !important;
        top: 0 !important;
    }
}

@media (min-width: @mobile-breakpoint-max+1) {
    .modal.component-backend-modal .modal-content {
        &.show-modal-frame {
            -webkit-animation: modal-showframe 0.4s;
            animation: modal-showframe 0.4s;
        }
    }

    @-webkit-keyframes modal-showframe {
        0%,
        50% {
            box-shadow: @box-shadow-z2, 0 0 0 3px #6a6cf7;
        }

        25%,
        75% {
            box-shadow: @box-shadow-z2;
        }
    }

    @keyframes modal-showframe {
        0%,
        50% {
            box-shadow: @box-shadow-z2, 0 0 0 3px #6a6cf7;
        }

        25%,
        75% {
            box-shadow: @box-shadow-z2;
        }
    }
}

@media (max-width: @mobile-breakpoint-max) {
    .modal.component-backend-modal:not(.non-modal) {
        .modal-dialog {
            margin: 0;

            .modal-content {
                border: none;
                top: 0 !important;
                left: 0 !important;
                .box-shadow(@box-shadow-symmetrical-z2);

                .border-bottom-radius(8px);
                .border-top-radius(0);
            }
        }
    }
}

body.modal-dragging {
    * {
        user-select: none;
    }
}

body.modal-dragging {
    * {
        cursor: move !important;
    }

    &.modal-dragging-ns * {
        cursor: ns-resize !important;
    }

    &.modal-dragging-ew * {
        cursor: ew-resize !important;
    }

    &.modal-dragging-nesw * {
        cursor: nesw-resize !important;
    }

    &.modal-dragging-nwse * {
        cursor: nwse-resize !important;
    }
}
