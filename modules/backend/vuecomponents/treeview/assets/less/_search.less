.treeview-search-control {
    height: 40px;
    background: @primary-bg;
    color: @text-color;
    position: relative;

    &:before {
        display: block;
        width: 15px;
        height: 15px;
        position: absolute;
        background-position: -238px 0;
        top: 14px;
        right: 11px;
    }

    > input {
        display: block;
        border: none;
        height: 40px;
        width: 100%;
        border-top: 1px solid @primary-border;
        background: transparent;
        color: @input-color;
        outline: none;
        -webkit-appearance: none;
        padding-left: 10px;
        padding-right: 35px;
        position: relative;
        z-index: 0;

        &:focus {
            border-top: 1px solid @color-focus;
        }
    }

    > button {
        display: none;
        -webkit-appearance: none;
        border: none;
        border-radius: 30px;
        position: absolute;
        width: 30px;
        height: 30px;
        right: 6px;
        top: 6px;
        background: transparent;
        .backend-hide-text();
        padding: 0;
        outline: none;

        &:focus {
            background: @toolbar-focus-bg;
        }

        &:before {
            display: block;
            width: 11px;
            height: 11px;
            position: absolute;
            background-position: -253px 0;
            top: 10px;
            right: 9px;
        }
    }

    &.search-enabled {
        &:before {
            display: none;
        }

         > button {
            display: block;
         }
    }
}