<tr class="inspector-control-row" :class="{'has-errors': hasErrors, 'hide-bottom-border': bottomBorderHidden}" v-show="rowVisible">
    <th v-if="!isFullWidth && !controlLabelHidden" :style="titlePanelStyle">
        <div class="inspector-label-container" :class="{'has-description': control.description}">
            <label
                class="inspector-padding-control-left"
                v-text="control.title"
                v-bind:for="controlEditorId"
                :style="labelStyle"
                @click.stop="onLabelClick"
            ></label>
            <span
                v-if="control.description"
                class="property-description backend-icon-background-pseudo"
                v-bind:data-tooltip-text="control.description"
            ></span>
        </div>
    </th>

    <td v-bind:colspan="controlColspan">
        <div class="full-width-control-label" v-if="isFullWidth && (control.title || control.description) && ! controlLabelHidden">
            <label
                class="inspector-padding-control-left"
                v-text="control.title"
                v-bind:for="controlEditorId"
                :style="labelStyle"
                @click.stop="onLabelClick"
            ></label>
            <div
                v-if="control.description"
                class="inspector-padding-control-left full-width-property-description"
                v-text="control.description"
                :style="labelStyle"
            ></div> 
        </div>

        <div :class="{'inspector-control-container': !isFullWidth, 'no-property-title': !control.title && !control.description}">
            <div v-if="!isFullWidth" class="inspector-drag-handle"></div>

            <?= $this->makePartial('controlhost-row-controls') ?>
        </div>
    </td>
</tr>