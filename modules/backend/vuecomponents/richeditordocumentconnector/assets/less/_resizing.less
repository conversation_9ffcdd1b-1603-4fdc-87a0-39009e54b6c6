.top-ruler {
    background-color: @document-ruler-bg;
    height: 14px;
    border-bottom: @document-ruler-bg 1px solid;

    .width-indicator {
        height: 13px;
        max-width: 100% !important;
        background: @document-ruler-color;
        position: relative;

        .width-tick {
            position: absolute;
            width: 1px;
            background: @document-ruler-tick;

            &.tick-major {
                top: 2px;
                height: 9px;
            }

            &.tick-minor {
                top: 5px;
                height: 3px;
            }
        }

        .width-drag-handle {
            .triangle(down, 15px, 8px, #3498DB);
            width: 15px;
            position: absolute;
            right: -7px;
            bottom: 0;
            opacity: 0.85;
            cursor: ew-resize;
        }
    }
}

&.resizing-ui {
    .fr-box.fr-basic {
        background-color: @body-bg;
    }

    .fr-wrapper {
        background: @input-bg;
        color: @text-color;
        box-shadow: 0 0 5px 3px rgba(var(--bs-body-color-rgb), 0.05);
        display: block;
        max-width: 100% !important;
        left: 50% !important;
    }
}
