@import "../../../../assets/less/core/boot.less";

//
// Filters
// --------------------------------------------------

@color-filter-text:            var(--bs-secondary-color);
@color-filter-text-active:     var(--bs-emphasis-color);
@color-filter-border:          var(--bs-border-color);
@color-filter-bg:              var(--bs-body-bg);
@color-filter-bg-active:       var(--bs-success);
@color-filter-items-bg-hover:  var(--oc-selection);

//
// Filters
// --------------------------------------------------

@import "filter.checkbox.less";
@import "filter.dropdown.less";
@import "filter.inline.less";
@import "filter.group.less";
@import "filter.box.less";

.control-filter {
    padding: 0 (@padding-standard / 2);
    color: @color-filter-text;
    background-color: @color-filter-bg;
    border-top: 1px solid @color-filter-border;
    border-bottom: 1px solid @color-filter-border;
    font-size: @font-size-base;

    a {
        text-decoration: none;
        color: @color-filter-text;
    }

    > .filter-setup {
        padding: 6px 0;

        > a {
            > span {
                margin-left: -2px;
                margin-right: -2px;
                display: block;
                color: @toolbar-color;
                width: 24px;
                height: 24px;
                text-align: center;
                border-radius: 3px;
                > i {
                    position: relative;
                    top: 2px;
                    font-size: 20px;
                }
            }

            &:hover > span {
                color: @toolbar-hover-color;
                background: @toolbar-hover-bg;
            }
        }
    }

    > .filter-group {
        display: inline-block;
        vertical-align: middle;
    }

    > .filter-group > .filter-scope {
        padding: 8px;
        display: block;

        .filter-label {
            margin-right: 5px;
        }

        .filter-setting {
            display: inline-block;
            margin-right: 5px;
            .transition(color 0.6s);
        }

        &.loading-indicator-container.in-progress {
            pointer-events: none;
            cursor: default;

            .loading-indicator {
                background: transparent;

                > span {
                    left: unset;
                    right: 0;
                    top: 10px;
                    background-color: @color-filter-bg;
                    border-radius: 50%;
                    margin-top: 0;
                    width: 20px;
                    height: 20px;
                    background-size: 15px 15px;
                }
            }
        }

        &:after {
            font-size: 14px;
            .icon(@icon-angle-down);
        }

        &.active {
            .filter-setting {
                padding-left: 5px;
                padding-right: 5px;
                color: #FFF;
                background-color: @color-filter-bg-active;
                .border-radius(4px);
                .transition(~'color 1s, background-color 1s');
            }
        }

        &:hover, &.active {
            color: @color-filter-text-active;

            .filter-label {
                color: @color-filter-text-active;
            }

            &.active .filter-setting {
                background-color: @color-filter-bg-active;
            }
        }
    }

    > .filter-group > .filter-has-popover {
        display: inline-block;
        padding: (@padding-standard / 2);
        .filter-setting {
            display: inline-block;
            .transition(color 0.6s);
        }

        &:after {
            font-size: 14px;
            .icon(@icon-angle-down);
        }

        &.active {
            .filter-setting {
                padding-left: 5px;
                padding-right: 5px;
                color: #FFF;
                background-color: @color-filter-bg-active;
                .border-radius(4px);
                transition: color 1s, background-color 1s;
            }
        }

        &:hover {
            color: #000;
            .filter-label {
                color: @color-filter-text;
            }
            &.active .filter-setting {
                background-color: darken(#86cB43, 5%);
            }
        }
    }
}

.control-filter.is-loading {
    cursor: wait;

    > .filter-group {
        pointer-events: none;
        opacity: .7;
    }

    > .filter-setup {
        > a {
            opacity: 0;
        }

        &:after {
            background-image: url('../../../../../backend/assets/images/loader-transparent.svg');
            position: absolute;
            content: ' ';
            width: 20px;
            height: 20px;
            left: 50%;
            top: 50%;
            margin-top: -10px;
            margin-left: -10px;
            display: block;
            background-size: 20px 20px;
            background-position: 50% 50%;
            animation: spin 1s linear infinite;
        }
    }
}

// .layout-row > .control-filter {
//     margin: 0 @padding-standard;
//     background-color: transparent;
//     border-top: none;
// }

// @media (max-width: @screen-xs-max) {
//     .layout-row > .control-filter {
//         margin: 0;
//     }
// }

.control-filter-popover {
    min-width: 275px;

    > .loading-indicator-container > .loading-indicator {
        border-radius: 4px;

        > span {
            left: 10px;
        }
    }

    // Hide spinner on numbers
    input[type="number"] {
        -moz-appearance: textfield;
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .filter-buttons {
        border-top: 1px solid @color-filter-border;
        padding: 10px;
        display: flex;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;

        > div {
            flex-grow: 1;
        }

        > .btn {
            text-align: center;
        }
    }
}
