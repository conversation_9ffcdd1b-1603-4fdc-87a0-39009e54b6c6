# ===================================
#  Column Definitions
# ===================================

columns:

    first_name:
        label: backend::lang.user.first_name
        searchable: true
        invisible: true

    last_name:
        label: backend::lang.user.last_name
        searchable: true
        invisible: true

    full_name:
        label: backend::lang.user.full_name
        select: concat(first_name, ' ', last_name)
        searchable: true
        invisible: true

    login:
        label: Username
        searchable: true
        width: 15%

    email:
        label: backend::lang.user.email
        searchable: true

    groups:
        label: backend::lang.user.groups
        relation: groups
        select: name
        sortable: false

    role:
        label: backend::lang.user.role.name
        relation: role
        select: name
        sortable: true
        searchable: true

    last_login:
        label: backend::lang.user.last_login
        searchable: true
        type: datetime

    created_at:
        label: backend::lang.user.created_at
        searchable: true
        invisible: true
        type: datetime

    updated_at:
        label: backend::lang.user.updated_at
        searchable: true
        invisible: true
        type: datetime

    deleted_at:
        label: backend::lang.user.deleted_at
        searchable: true
        invisible: true
        type: datetime

    is_activated:
        label: backend::lang.user.activated
        invisible: true
        type: switch

    is_superuser:
        label: backend::lang.user.superuser
        invisible: true
        type: switch
