{"Check For Updates": "Check For Updates", "Install Packages": "Install Packages", "Manage Themes": "Manage Themes", "Manage Plugins": "Manage Plugins", "Project": "Project", "Owner": "Owner", "Plugins": "Plugins", "Recommended": "Recommended", "Disabled": "Disabled", "Current Build": "Current Build", "Updates Available": "Updates Available", "Up to Date": "Up to Date", "Latest Build": "Latest Build", "View Changelog": "View Changelog", "System Updates": "System Updates", "Update the system modules and plugins.": "Update the system modules and plugins.", "General": "General", "Mail": "Mail", "Utilities": "Utilities", "Settings": "Settings", "Show All Settings": "Show All Settings", "Unable to find the specified settings.": "Unable to find the specified settings.", "The settings page is missing a Model definition.": "The settings page is missing a Model definition.", ":name settings updated": ":name settings updated", "Return to System Settings": "Return to System Settings", "Find a Setting...": "Find a Setting...", "Disable mail branding CSS": "Disable mail branding CSS", "Manage Sites": "Manage Sites", "Manage the websites available for this application.": "Manage the websites available for this application.", "Marketplace": "Marketplace", "There is no documentation provided.": "There is no documentation provided.", "Documentation": "Documentation", "Upgrade Guide": "Upgrade Guide", "License": "License", "Attach to Project": "Attach to Project", "Manage Updates": "Manage Updates", "Software Update": "Software Update", "Return to System Updates": "Return to System Updates", "Try Again": "Try Again", "Unpacking application files": "Unpacking application files", "Update Failed": "Update Failed", "Unpacking plugin: :name": "Unpacking plugin: :name", "The primary site is used by default and cannot be deleted.": "The primary site is used by default and cannot be deleted.", "Disabled sites are not shown on the frontend.": "Disabled sites are not shown on the frontend.", "Enabled in the Admin Panel": "Enabled in the Admin Panel", "Configuration": "Configuration", "Use this if you want the site to be enabled in the admin panel.": "Use this if you want the site to be enabled in the admin panel.", "Install": "Install", "Sync Project": "Sync Project", "Name": "Name", "Unique Code": "Unique Code", "Theme": "Theme", "Sites": "Sites", "Create Site": "Create Site", "Base URL": "Base URL", "Status": "Status", "Current default value: :value": "Current default value: :value", "Locale": "Locale", "Timezone": "Timezone", "Custom application URL": "Custom application URL", "Override the application URL when this site is active.": "Override the application URL when this site is active.", "Use a CMS route prefix": "Use a CMS route prefix", "A prefix can identify this site when using a shared hostname.": "A prefix can identify this site when using a shared hostname.", "Define matching hostnames": "Define matching hostnames", "Specify domain names and patterns that must be used to serve this site.": "Specify domain names and patterns that must be used to serve this site.", "Display a style for this site": "Display a style for this site", "To help identify this site, display a color in the admin panel.": "To help identify this site, display a color in the admin panel.", "Save": "Save", "Back": "Back", "Edit": "Edit", "Refresh": "Refresh", "Save and Close": "Save and Close", "Use Default": "Use Default", "Use Custom": "Use Custom", "Specify a custom locale code.": "Specify a custom locale code.", "Failed": "Failed", "or": "or", "Code": "Code", "October CMS Marketplace": "October CMS Marketplace", "Visit the :link to add some.": "Project has no plugins or themes. Visit the :link to add some.", "Buy Now": "Buy Now", "Updating package manager": "Updating package manager", "Updating application files": "Updating application files", "Setting build number": "Setting build number", "Finishing update process": "Finishing update process", "Installing plugin: :name": "Installing plugin: :name", "Finishing installation process": "Finishing installation process", "Removing theme: :name": "Removing theme: :name", "Please specify a Theme name to install.": "Please specify a Theme name to install.", "Installing theme: :name": "Installing theme: :name", "Extracting theme: :name": "Extracting theme: :name", "Seeding theme: :name": "Seeding theme: :name", "Removing plugin: :name": "Removing plugin: :name", "Please specify a Plugin name to install.": "Please specify a Plugin name to install.", "Update process complete": "Update process complete", "Package installed successfully": "Package installed successfully", "Check Dependencies": "Check Dependencies", "Install Dependencies": "Install Dependencies", "There are missing dependencies needed for the system to run correctly.": "There are missing dependencies needed for the system to run correctly.", "License Key": "License Key", "How to find your License Key": "How to find your License Key", "The password attribute is required.": "The password attribute is required.", "The login attribute is required.": "The login attribute is required.", "Cannot login user since they are not activated.": "Cannot login user since they are not activated.", "Cannot login user since they are banned.": "Cannot login user since they are banned.", "Cannot login user since they are suspended.": "Cannot login user since they are suspended.", "A user was not found with the given credentials.": "A user was not found with the given credentials.", "A user was found but the password did not match.": "A user was found but the password did not match.", "User is not logged in": "User is not logged in", "Register Software": "Register Software", "View the Dashboard": "View the Dashboard", "Set the Default Dashboard": "Set the Default Dashboard", "Log File": "Log File", "Sendmail": "Sendmail", "SMTP": "SMTP", "Mailgun": "Mailgun", "SES": "SES", "Postmark": "Postmark", "No encryption": "No encryption", "TLS": "TLS", "Mail Configuration": "Mail Configuration", "Manage email configuration.": "Manage email configuration.", "Sender Name": "Sender Name", "Sender Email": "Sender <PERSON><PERSON>", "Mail Method": "Mail Method", "SMTP Address": "SMTP Address", "SMTP Port": "SMTP Port", "SMTP Encryption Protocol": "SMTP Encryption Protocol", "SMTP Authorization Required": "SMTP Authorization Required", "Use this checkbox if your SMTP server requires authorization.": "Use this checkbox if your SMTP server requires authorization.", "Username": "Username", "Password": "Password", "Sendmail Path": "Sendmail Path", "Please specify the path of the sendmail program.": "Please specify the path of the sendmail program.", "Mailgun Domain": "Mailgun Domain", "Please specify the Mailgun domain name.": "Please specify the Mailgun domain name.", "Mailgun Secret": "Mailgun Secret", "Enter your Mailgun API key.": "Enter your Mailgun API key.", "SES Key": "SES Key", "Enter your SES API key": "Enter your SES API key", "SES Secret": "SES Secret", "Enter your SES API secret key": "Enter your SES API secret key", "SES Region": "SES Region", "Enter your SES region (e.g. us-east-1)": "Enter your SES region (e.g. us-east-1)", "Postmark Token": "Postmark Token", "Enter your Postmark API secret key": "Enter your Postmark API secret key", "Define administrator roles": "Define administrator roles", "Restrict access to this site to only administrator with the following roles.": "Restrict access to this site to only administrator with the following roles.", "Group": "Group", "Create Group": "Create Group", "Site Group": "Site Group", "Site Groups": "Site Groups", "Manage Site Groups": "Manage Site Groups", "Group Name": "Group Name", "All Sites": "All Sites", "Manage Backend Preferences": "Manage Backend Preferences", "Manage Code Editor Preferences": "Manage Code Editor Preferences", "Customize Backend Styles": "Customize Backend Styles", "Mail Templates": "Mail Templates", "Modify the mail templates that are sent to users and administrators, manage email layouts.": "Modify the mail templates that are sent to users and administrators, manage email layouts.", "Mail Branding": "Mail Branding", "Modify the colors and appearance of mail templates.": "Modify the colors and appearance of mail templates.", "Event Log": "Event Log", "View system log messages with their recorded time and details.": "View system log messages with their recorded time and details.", "Request Log": "Request Log", "View bad or redirected requests, such as Page not found (404).": "View bad or redirected requests, such as <PERSON> not found (404).", "Log Settings": "Log Settings", "Specify which areas should use logging.": "Specify which areas should use logging.", "Test message sent.": "Test message sent.", "New Layout": "New Layout", "New Partial": "New Partial", "New Template": "New Template", "Template": "Template", "Templates": "Templates", "Layouts": "Layouts", "Partials": "Partials", "Partial": "Partial", "Mail Partials": "Mail Partials", "Layout": "Layout", "Mail Layouts": "Mail Layouts", "Unique code used to refer to this template": "Unique code used to refer to this template", "-- No layout --": "-- No layout --", "HTML": "HTML", "CSS": "CSS", "Plaintext": "Plaintext", "Disable automatic inline CSS": "Disable automatic inline CSS", "Options": "Options", "Subject": "Subject", "Email message subject": "Email message subject", "Description": "Description", "Drivers Not Installed": "Drivers Not Installed", "This mail method requires the plugin \":plugin\" be installed before you can send mail.": "This mail method requires the plugin \":plugin\" be installed before you can send mail.", "ID": "ID", "Event ID": "Event ID", "Level": "Level", "Date & Time": "Date & Time", "Message": "Message", "Logging": "Logging", "Log Bad Requests": "Log Bad Requests", "Browser requests that may require attention, such as 404 errors.": "Browser requests that may require attention, such as 404 errors.", "Log Theme Changes": "Log Theme Changes", "When a change is made to the theme using the backend.": "When a change is made to the theme using the backend.", "Log System Events": "Log System Events", "Store system events in the database in addition to the file-based log.": "Store system events in the database in addition to the file-based log.", "Background": "Background", "Body background": "Body background", "Content background": "Content background", "Inner content background": "Inner content background", "Buttons": "Buttons", "Button text color": "Button text color", "Primary button background": "Primary button background", "Positive button background": "Positive button background", "Negative button background": "Negative button background", "Typography": "Typography", "Header color": "Header color", "Headings color": "Headings color", "Text color": "Text color", "Link color": "Link color", "Footer color": "Footer color", "Borders": "Borders", "Body border color": "Body border color", "Subcopy border color": "Subcopy border color", "Table border color": "Table border color", "Components": "Components", "Panel background": "Panel background", "Promotion background": "Promotion background", "Promotion border color": "Promotion border color", "Customize Mail Appearance": "Customize Mail Appearance", "Version": "Version", "Enabled": "Enabled", "Latest": "Latest", "Log ID": "Log ID", "Counter": "Counter", "Referers": "Referrers", "URL": "URL", "This log displays a list of potential errors that occur in the application, such as exceptions and debugging information.": "This log displays a list of potential errors that occur in the application, such as exceptions and debugging information.", "Event": "Event", "This log displays a list of browser requests that may require attention. For example, if a visitor opens a CMS page that cannot be found, a record is created with the status code 404.": "This log displays a list of browser requests that may require attention. For example, if a visitor opens a CMS page that cannot be found, a record is created with the status code 404.", "There were no detected referrers to this URL.": "There were no detected referrers to this URL.", "Request": "Request", "Event log emptied": "Event log emptied", "Empty Event Log": "Empty Event Log", "Emptying Event Log...": "Emptying Event Log...", "Return to Event Log": "Return to Event Log", "Installed": "Installed", "Primary Site": "Primary Site", "Refresh Data": "Refresh Data", "Remove Data": "Remove Data", "Are you sure you want to reset the selected plugins? This will reset each plugin's data, restoring it to the initial install state.": "Are you sure you want to reset the selected plugins? This will reset each plugin's data, restoring it to the initial install state.", "Reset Plugin Data": "Reset Plugin Data", "Enable Plugins": "Enable Plugins", "Are you sure you want to :action these plugins?": "Are you sure you want to :action these plugins?", "enable": "enable", "disable": "disable", "Disable Plugins": "Disable Plugins", "Select Action...": "Select Action...", "Data has been removed.": "Data has been removed.", "Plugin has been removed from the file system.": "Plugin has been removed from the file system.", "search plugins to install...": "search plugins to install...", "Plugin has been disabled by configuration.": "Plugin has been disabled by configuration.", "Plugin has missing dependencies or disabled by system.": "Plugin has missing dependencies or disabled by system.", "search themes to install...": "search themes to install...", "Install Theme": "Install Theme", "Theme Name": "Theme Name", "Install Plugin": "Install Plugin", "Plugin Name": "Plugin Name", "Name the plugin by its unique code. For example, RainLab.Blog": "Name the plugin by its unique code. For example, RainLab.Blog", "Name the theme by its unique code. For example, RainLab.Vanilla": "Name the theme by its unique code. For example, RainLab.Vanilla", "Select Installation Method": "Select Installation Method", "Make a Copy (Recommended)": "Make a Copy (Recommended)", "Take a copy of this theme to customize it and manually manage future updates.": "Take a copy of this theme to customize it and manually manage future updates.", "Install with Composer": "Install with Composer", "Extend the theme using a child theme to preserve future updates from the theme author.": "Extend the theme using a child theme to preserve future updates from the theme author.", "Seed Theme Data": "Seed Theme Data", "Import the blueprints files, language files and database contents for this theme, if applicable. You can skip this and do it later via the Frontend Theme settings.": "Import the blueprints files, language files and database contents for this theme, if applicable. You can skip this and do it later via the Frontend Theme settings.", "Change Status": "Change Status", "Site Definitions": "Site Definitions", "The resizer file ':name' is not found.": "The resizer file ':name' is not found.", "The combiner file ':name' is not found.": "The combiner file ':name' is not found."}