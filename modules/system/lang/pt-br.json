{"Check For Updates": "Verificar atualizações", "Install Packages": "Instalar pacotes", "Manage Themes": "Gerenciar temas", "Manage Plugins": "Gerenciar plugins", "Project": "Projeto", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "Plugins": "Plugins", "Recommended": "Recomendado", "Disabled": "Desabilitados", "Current Build": "Compilação atual", "Updates Available": "Atualizações disponíveis", "Up to Date": "Atualizado", "Latest Build": "<PERSON><PERSON><PERSON> mais recente", "View Changelog": "Visualizar Registro de mudanças", "System Updates": "Atualizações", "Update the system modules and plugins.": "Atualize o sistema, gerencie e instale plugins e temas.", "General": "G<PERSON>", "Mail": "Email", "Utilities": "Utilidades", "Settings": "Configurações", "Show All Settings": "<PERSON><PERSON> todas as configura<PERSON><PERSON><PERSON>", "Unable to find the specified settings.": "Impossível encontrar as configurações solicitadas.", "The settings page is missing a Model definition.": "Falta uma definição de modelo na página de configurações.", ":name settings updated": "Configurações para :name foram atualizados com sucesso.", "Return to System Settings": "Retornar para as configurações do sistema", "Find a Setting...": "Encontrar uma configuração...", "Disable mail branding CSS": "Desabilitar CSS de branding de e-mail", "Manage Sites": "Gerenciar sites", "Manage the websites available for this application.": "Gerencie os sites disponíveis para este aplicativo.", "Marketplace": "Marketplace", "There is no documentation provided.": "Não foi fornecida nenhuma documentação.", "Documentation": "Documentação", "Upgrade Guide": "Guia de atualização", "License": "Licença", "Attach to Project": "Anexar ao projeto", "Manage Updates": "Gerenciar atualizações", "Software Update": "Atualização de software", "Return to System Updates": "Voltar às atualizações", "Try Again": "Tentar novamente", "Unpacking application files": "Desempacotando arquivos do aplicativo", "Update Failed": "Falha na atualização", "Unpacking plugin: :name": "Desempacotando o plugin: :name", "The primary site is used by default and cannot be deleted.": "O site principal é usado por padrão e não pode ser excluído.", "Disabled sites are not shown on the frontend.": "Disabled sites are not shown on the frontend.", "Enabled in the Admin Panel": "Ativado no Painel de Administração", "Configuration": "Configuração", "Use this if you want the site to be enabled in the admin panel.": "Use isto se você quiser que o site seja ativado no painel de administração.", "Install": "Instalar", "Sync Project": "Sincronizar Projeto", "Name": "Nome", "Unique Code": "<PERSON><PERSON><PERSON>", "Theme": "<PERSON><PERSON>", "Sites": "Sites", "Create Site": "Criar Site", "Base URL": "URL Base", "Status": "Status", "Current default value: :value": "<PERSON>or padrão atual: :value", "Locale": "Idioma", "Timezone": "<PERSON><PERSON>", "Custom application URL": "URL da aplicação personalizada", "Override the application URL when this site is active.": "Override the application URL when this site is active.", "Use a CMS route prefix": "Use a CMS route prefix", "A prefix can identify this site when using a shared hostname.": "A prefix can identify this site when using a shared hostname.", "Define matching hostnames": "Definir hostnames correspondentes", "Specify domain names and patterns that must be used to serve this site.": "Specify domain names and patterns that must be used to serve this site.", "Display a style for this site": "Display a style for this site", "To help identify this site, display a color in the admin panel.": "To help identify this site, display a color in the admin panel.", "Save": "<PERSON><PERSON>", "Save and Close": "<PERSON><PERSON> e <PERSON>char", "Use Default": "<PERSON><PERSON>", "Use Custom": "Use Custom", "Specify a custom locale code.": "Especificar um código de localidade personalizado.", "Failed": "Fal<PERSON>", "or": "ou", "Code": "Código", "October CMS Marketplace": "October CMS Marketplace", "Visit the :link to add some.": "Project has no plugins or themes. Visit the :link to add some.", "Buy Now": "Comprar agora", "Updating package manager": "Atualizando gerenciador de paco<PERSON>", "Updating application files": "Baixando arquivos do aplicativo", "Setting build number": "Configurando o número de compilação", "Finishing update process": "Finalizando processo de atualização", "Installing plugin: :name": "Baixando o plugin: :name", "Finishing installation process": "Finalizando processo de instalação", "Removing theme: :name": "Removendo tema: :name", "Please specify a Theme name to install.": "Por favor, especifique um nome de tema para instalar.", "Installing theme: :name": "Baixando o tema: :name", "Extracting theme: :name": "Extracting theme: :name", "Seeding theme: :name": "Seeding theme: :name", "Removing plugin: :name": "Removendo o plugin: :name", "Please specify a Plugin name to install.": "Por favor, especifique um nome de plugin para instalar.", "Update process complete": "O processo de atualização foi realizado com sucesso.", "Package installed successfully": "O plugin foi instalado com sucesso.", "Check Dependencies": "Verificar Dependências", "Install Dependencies": "Instalar Dependências", "There are missing dependencies needed for the system to run correctly.": "There are missing dependencies needed for the system to run correctly.", "License Key": "Chave de Li<PERSON>nça", "How to find your License Key": "Como encontrar sua chave da licença", "The password attribute is required.": "O atributo da senha é obrigatório.", "The login attribute is required.": "O atributo do login é obrigatório.", "Cannot login user since they are not activated.": "Cannot login user since they are not activated.", "Cannot login user since they are banned.": "Cannot login user since they are banned.", "Cannot login user since they are suspended.": "Cannot login user since they are suspended.", "A user was not found with the given credentials.": "A user was not found with the given credentials.", "A user was found but the password did not match.": "A user was found but the password did not match.", "User is not logged in": "O usuário não está logado", "Register Software": "Registrar Software", "View the Dashboard": "<PERSON><PERSON>r o <PERSON>el", "Set the Default Dashboard": "Gerenciar o painel padrão", "Log File": "Arquivo de registro", "Sendmail": "Sendmail", "SMTP": "SMTP", "Mailgun": "Mailgun", "SES": "SES", "Postmark": "Postmark", "No encryption": "Sem criptografia", "TLS": "TLS", "Mail Configuration": "Configurações de E-mail", "Manage email configuration.": "Gerenciar configurações de e-mail.", "Sender Name": "Nome do Remetente", "Sender Email": "E-mail do Remetente", "Mail Method": "<PERSON><PERSON><PERSON><PERSON>", "SMTP Address": "Endereço SMTP", "SMTP Port": "Porta SMTP", "SMTP Encryption Protocol": "Protocolo de criptografia SMTP", "SMTP Authorization Required": "Autenticação SMTP obrigatória", "Use this checkbox if your SMTP server requires authorization.": "Use esta opção se o seu servidor SMTP requer autenticação.", "Username": "<PERSON><PERSON><PERSON><PERSON>", "Password": "<PERSON><PERSON>", "Sendmail Path": "<PERSON>in<PERSON> do Sendmail", "Please specify the path of the sendmail program.": "Por favor, especifique o caminho do programa Sendmail.", "Mailgun Domain": "Domínio do <PERSON>gun", "Please specify the Mailgun domain name.": "Por favor, forneça o domínio do Mailgun.", "Mailgun Secret": "Mailgun Secret", "Enter your Mailgun API key.": "Forneça sua chave de API do Mailgun.", "SES Key": "Chave SES", "Enter your SES API key": "Forneça sua chave do SES", "SES Secret": "SES Secret", "Enter your SES API secret key": "Forneça sua chave de API do SES.", "SES Region": "Região SES", "Enter your SES region (e.g. us-east-1)": "Entre com sua região SES (exemplo: us-east-1)", "Postmark Token": "Postmark chave secreta", "Enter your Postmark API secret key": "Insira sua chave secreta da API Postmark", "Define administrator roles": "Definir funções de administrador", "Restrict access to this site to only administrator with the following roles.": "Restrict access to this site to only administrator with the following roles.", "Group": "Group", "Create Group": "Create Group", "Site Group": "Site Group", "Site Groups": "Site Groups", "Manage Site Groups": "Manage Site Groups", "Group Name": "Group Name", "All Sites": "All Sites", "Manage Backend Preferences": "Gerenciar preferências da área administrativa", "Manage Code Editor Preferences": "Gerenciar preferências do editor de código", "Customize Backend Styles": "<PERSON><PERSON><PERSON> o <PERSON>el", "Mail Templates": "Modelos de E-mail", "Modify the mail templates that are sent to users and administrators, manage email layouts.": "Modificar os modelos dos e-mails que são enviados para usuários e administradores.", "Mail Branding": "<PERSON><PERSON><PERSON>", "Modify the colors and appearance of mail templates.": "Modifique as cores e a aparência dos modelos de email.", "Event Log": "Registro de Eventos", "View system log messages with their recorded time and details.": "Visualize as mensagens do sistema, com horário e detalhes.", "Request Log": "Registro de Requisições", "View bad or redirected requests, such as Page not found (404).": "Visualize requisições malsucedidas na aplicação, como Página não encontrada (404).", "Log Settings": "Configurações de registros", "Specify which areas should use logging.": "Especifique quais áreas devem usar o registro.", "Test message sent.": "Mensagem de teste enviada com sucesso.", "New Layout": "Novo esboço", "New Partial": "Novo bloco", "New Template": "Novo modelo", "Template": "<PERSON><PERSON>", "Templates": "Modelos", "Layouts": "Esboços", "Partials": "Blocos", "Partial": "Bloco", "Mail Partials": "Blocos de e-mail", "Layout": "Esboço", "Mail Layouts": "Esboços de e-mail", "Unique code used to refer to this template": "Código exclusivo usado para se referir a este modelo", "-- No layout --": "-- <PERSON><PERSON> es<PERSON> --", "HTML": "HTML", "CSS": "CSS", "Plaintext": "Texto Simples", "Disable automatic inline CSS": "Desativar CSS inline automático", "Options": "Opções", "Subject": "<PERSON><PERSON><PERSON>", "Email message subject": "<PERSON><PERSON><PERSON> da mensagem", "Description": "Descrição", "Drivers Not Installed": "Drivers não instalados", "This mail method requires the plugin \":plugin\" be installed before you can send mail.": "Este método requer que o plugin \":plugin\" esteja instalado.", "ID": "ID", "Event ID": "Identificador do Evento", "Level": "Nível", "Date & Time": "Data & Hora", "Message": "Mensagem", "Logging": "Registros", "Log Bad Requests": "Registrar requisiç<PERSON><PERSON> inválid<PERSON>", "Browser requests that may require attention, such as 404 errors.": "Requisições do navegador que podem exigir atenção, como erros 404.", "Log Theme Changes": "Registrar alterações no tema", "When a change is made to the theme using the backend.": "Quando uma alteração é feita no tema usando o backend.", "Log System Events": "Registrar eventos do sistema", "Store system events in the database in addition to the file-based log.": "Armazene eventos do sistema no banco de dados, além do registro baseado em arquivo.", "Background": "Fundo", "Body background": "Fundo do corpo", "Content background": "Fundo do conteúdo", "Inner content background": "Fundo de conteúdo interno", "Buttons": "<PERSON><PERSON><PERSON><PERSON>", "Button text color": "Cor do texto do botão", "Primary button background": "Fundo do botão principal", "Positive button background": "Fundo do botão positivo", "Negative button background": "Fundo do botão negativo", "Typography": "Tipografia", "Header color": "Cor do cabeçalho", "Headings color": "<PERSON><PERSON> títulos", "Text color": "Cor do texto", "Link color": "Cor do link", "Footer color": "Cor do rod<PERSON>", "Borders": "<PERSON><PERSON><PERSON>", "Body border color": "Cor da borda do corpo", "Subcopy border color": "<PERSON><PERSON> da borda da subcópia", "Table border color": "<PERSON><PERSON> <PERSON> borda da tabela", "Components": "Componentes", "Panel background": "Fundo do painel", "Promotion background": "Fundo do cupom promocional", "Promotion border color": "Cor da borda cupom promocional", "Customize Mail Appearance": "Personalizar aparência de email", "Version": "Vers<PERSON>", "Enabled": "Plug<PERSON>", "Latest": "<PERSON><PERSON> recentes", "Log ID": "ID do registro", "Counter": "<PERSON><PERSON><PERSON>", "Referers": "Referências", "URL": "URL", "This log displays a list of potential errors that occur in the application, such as exceptions and debugging information.": "Este registro mostra a lista dos potenciais erros que ocorreram na aplicação, como exceções e informações de depuração.", "Event": "Evento", "This log displays a list of browser requests that may require attention. For example, if a visitor opens a CMS page that cannot be found, a record is created with the status code 404.": "Este registro mostra uma lista de requisições que requerem atenção. Por exemplo, se um usuário solicitar uma página não encontrada, será registrado com o status 404.", "There were no detected referrers to this URL.": "There were no detected referrers to this URL.", "Request": "Requisição", "Event log emptied": "Registro de eventos esvaziado com sucesso.", "Empty Event Log": "Esvaziar registro de eventos", "Emptying Event Log...": "Esvaziando registro de eventos...", "Return to Event Log": "Retornar ao registro de eventos", "Installed": "Installed", "Primary Site": "Primary Site", "Refresh Data": "Refresh Data", "Remove Data": "Remove Data", "Are you sure you want to reset the selected plugins? This will reset each plugin's data, restoring it to the initial install state.": "Tem certeza de que deseja redefinir os plugins selecionados? Isso redefinirá os dados de cada plugin, restaurando-os para o estado de inicial.", "Reset Plugin Data": "Redefinir dados do plugin", "Enable Plugins": "Ativar plugins", "Are you sure you want to :action these plugins?": "Tem certeza de que deseja :action esses plugins?", "enable": "ativo", "disable": "inativo", "Disable Plugins": "Desativar plugins", "Select Action...": "Selecionar ação...", "Data has been removed.": "Data has been removed.", "Plugin has been removed from the file system.": "Plugin removido do sistema de arquivos.", "search plugins to install...": "Buscar plugin para instalar...", "Plugin has been disabled by configuration.": "Plugin foi desativado pela configuração.", "Plugin has missing dependencies or disabled by system.": "Plugin tem dependências ausentes ou desativados pelo sistema.", "search themes to install...": "Buscar temas para instalar...", "Install Theme": "Instalar tema", "Theme Name": "Nome do Tema", "Install Plugin": "Instalar plugin", "Plugin Name": "Nome do Plugin", "Name the plugin by its unique code. For example, RainLab.Blog": "Nomeie o plugin pelo seu código exclusivo. Por exemplo, RainLab.Blog", "Name the theme by its unique code. For example, RainLab.Vanilla": "Nome do tema deve ser único. <PERSON><PERSON> exemplo, Rain<PERSON>ab<PERSON>", "Select Installation Method": "Select Installation Method", "Make a Copy (Recommended)": "Make a Copy (Recommended)", "Take a copy of this theme to customize it and manually manage future updates.": "Take a copy of this theme to customize it and manually manage future updates.", "Install with Composer": "Install with Composer", "Extend the theme using a child theme to preserve future updates from the theme author.": "Extend the theme using a child theme to preserve future updates from the theme author.", "Seed Theme Data": "Seed Theme Data", "Import the blueprints files, language files and database contents for this theme, if applicable. You can skip this and do it later via the Frontend Theme settings.": "Import the blueprints files, language files and database contents for this theme, if applicable. You can skip this and do it later via the Frontend Theme settings.", "Change Status": "Change Status", "Site Definitions": "Site Definitions"}