<div>

    <!-- Search -->
    <form
        role="form"
        id="installThemesForm"
        onsubmit="$.oc.installProcess.searchSubmit(this); return false">
        <input type="hidden" name="type" value="theme" />
        <div class="product-search">
            <input
                name="code"
                id="themeSearchInput"
                class="product-search-input search-input-lg typeahead"
                placeholder="<?= __("search themes to install...") ?>"
                data-search-type="theme"
                />
            <i class="icon icon-search"></i>
            <i class="icon loading" style="display: none"></i>
        </div>
    </form>

    <!-- Browse Products -->
    <div class="browse-products-container">
        <div
            id="browseThemes"
            class="browse-products browse-themes"
            data-handler="onBrowsePackages"
            data-view-type="theme"
            data-view="theme/product"></div>

        <div
            id="browseThemesPagination"
            data-view="theme/pagination"></div>
    </div>

</div>

<script type="text/template" data-partial="theme/product">
    <a href="<?= Backend::url('system/market/theme') ?>/{{slug}}" class="product-card product-theme">
        <div class="image"><img src="{{image}}" alt=""></div>
        <div class="details">
            <h4>{{name}}</h4>
        </div>
        <div class="bottom-controls">
            <div class="author-details">
                {{code}} by {{author}}
            </div>
        </div>
    </a>
</script>

<script type="text/template" data-partial="theme/pagination">
    <?= $this->makePartial('pagination', ['pageUrl' => Backend::url('system/market/index/themes').'?theme_page' ]) ?>
</script>
