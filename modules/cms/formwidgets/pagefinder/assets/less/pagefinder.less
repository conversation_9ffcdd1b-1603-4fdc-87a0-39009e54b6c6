@import "../../../../../backend/assets/less/core/boot.less";

@import "pagefinder.control-ui.less";

.field-pagefinder {
    .record-item {
        position: relative;
        outline: none;
        display: block;
        margin: 4px;

        .record-data-container {
            padding: 4px 3px 3px;
        }

        .record-data-container-inner {
            position: relative;
        }

        .icon-container, .info, .info h4, .info h4 p {
            vertical-align: top;
            display: inline-block;
        }

        .icon-container {
            width: 20px;
            height: 20px;
            position: absolute;
            margin-top: 1px;

            i {
                position: relative;
                top: 2px;
                left: 3px;
            }
        }

        .info {
            padding-left: 22px;
        }

        .recordname, .description {
            color: @input-color;
            font-size: @font-size-base;
            word-break: break-all;
            margin-bottom: 0;
        }

        .description {
            font-size: .9rem;
            color: @secondary-color;
        }
    }
}
