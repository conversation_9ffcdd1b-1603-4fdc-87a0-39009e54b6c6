<ul class="tree-path">
    <li class="root"><a href="javascript:;" data-type="media-item" data-item-type="folder" data-path="/" data-clear-search="true"><?= e(trans('backend::lang.media.library')) ?></a></li>

    <?php if (!$searchMode): ?>
        <?php foreach ($pathSegments as $folder => $path): ?>
            <?php if ($path != '/'): ?>
                <li><a href="javascript:;" data-type="media-item" data-item-type="folder" data-path="<?= e($path) ?>"><?= basename($folder) ?></a></li>
            <?php endif ?>
        <?php endforeach?>
    <?php else: ?>
        <li><a href="javascript:;" data-type="media-item"><?= e(trans('backend::lang.media.search')) ?></a></li>
    <?php endif ?>
</ul>