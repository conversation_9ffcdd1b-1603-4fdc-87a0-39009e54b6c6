uuid: 7b193500-ac0b-481f-a79c-2a362646364d
name: Block Builder
handle: blockBuilder
type: mixin

fields:
    blocks:
        label: Blocks
        type: repeater
        displayMode: builder
        span: adaptive
        tab: Blocks
        groups:
            featurette:
                name: Featurette
                description: Large list of features with a screenshot
                icon: icon-diamond
                fields:
                    _mixin:
                        type: mixin
                        source: bbFeaturette

            headline:
                name: Big Headline
                description: Shows a big headline with a link
                icon: icon-text-h1
                fields:
                    _mixin:
                        type: mixin
                        source: bbHeadline

            # headline_items:
            #     name: Headline Items
            #     description: Usually follows a headline
            #     icon: icon-quote-right
            #     fields:
            #         _mixin:
            #             type: mixin
            #             source: bbHeadlineItems

            call_to_action:
                name: Call To Action
                description: A block that requests the user do something
                icon: icon-comment
                fields:
                    _mixin:
                        type: mixin
                        source: bbCallToAction

            carousel:
                name: Carousel
                description: A gallery of images
                icon: icon-picture
                fields:
                    _mixin:
                        type: mixin
                        source: bbCarousel

            # feature_table:
            #     name: Feature Table
            #     description: Headers with view detail links
            #     icon: icon-quote-right
            #     fields:
            #         _mixin:
            #             type: mixin
            #             source: bbFeatureTable

            # pricing_table:
            #     name: Pricing Table
            #     description: Display a table of available pricing
            #     icon: icon-quote-right
            #     fields:
            #         _mixin:
            #             type: mixin
            #             source: bbPricingTable

            # compare_table:
            #     name: Compare Table
            #     description: Compare items in a table
            #     icon: icon-quote-right
            #     fields:
            #         _mixin:
            #             type: mixin
            #             source: bbCompareTable


