<?php
    $label = $this->getPropertyValue($properties, 'label');
    $comment =$this->getPropertyValue($properties, 'oc.comment');
?>

<div class="builder-blueprint-control-section">
    <div class="builder-control-label"><?php if (strlen($label)): ?><span data-localization-key="<?= e($label) ?>" data-plugin="<?= e($formBuilder->getPluginCode()) ?>"><?= e(trans($label)) ?><?php endif ?></div>
    <div class="builder-control-comment-below"><?php if (strlen($comment)): ?><span data-localization-key="<?= e($comment) ?>" data-plugin="<?= e($formBuilder->getPluginCode()) ?>"><?= e(trans($comment)) ?></span><?php endif ?></div>
</div>