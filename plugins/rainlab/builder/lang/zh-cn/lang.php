<?php return [
  'plugin' => [
    'add' => '创建插件',
    'no_records' => '找不到插件',
    'no_name' => '没有名称',
    'search' => '搜索...',
    'filter_description' => '显示所有插件或只显示您的插件.',
    'settings' => '设置',
    'entity_name' => '插件',
    'field_name' => '名称',
    'field_author' => '作者',
    'field_description' => '描述',
    'field_icon' => '插件图标',
    'field_plugin_namespace' => '插件命名空间',
    'field_author_namespace' => '作者命名空间',
    'field_namespace_description' => '命名空间只能包含拉丁字母和数字，并且应该以拉丁字母开头。示例插件命名空间：Blog',
    'field_author_namespace_description' => '创建插件后，不能使用Builder更改命名空间。示例作者名称空间：JohnSmith',
    'tab_general' => '常规参数',
    'tab_description' => '描述',
    'field_homepage' => '插件主页',
    'no_description' => '常规参数没有为此插件提供说明',
    'error_settings_not_editable' => '无法使用生成器编辑此插件的设置.',
    'update_hint' => '您可以在“本地化”选项卡上编辑本地化插件的名称和说明.',
    'manage_plugins' => '创建和编辑插件',
  ],
  'author_name' => [
    'title' => '作者名称',
    'description' => '用于新插件的默认作者名称。作者名不是固定的-你可以在插件配置中随时更改它.',
  ],
  'author_namespace' => [
    'title' => '作者命名空间',
    'description' => '如果您是为市场开发的，命名空间应该与作者代码匹配，并且不能更改。有关详细信息，请参阅文档.',
  ],
  'database' => [
    'menu_label' => '数据库',
    'no_records' => '未找到数据库表',
    'search' => '搜索...',
    'confirmation_delete_multiple' => '删除选中的数据库表?',
    'field_name' => '数据库表名',
    'tab_columns' => '列',
    'column_name_name' => '列',
    'column_name_required' => '请提供列名',
    'column_name_type' => '类型',
    'column_type_required' => '请选择列类型',
    'column_name_length' => '长度',
    'column_validation_length' => '对于十进制列，长度值应为整数或指定为精度和小数位数（10,2）。长度列中不允许有空格.',
    'column_validation_title' => '列名中只允许数字、小写拉丁字母和下划线',
    'column_name_unsigned' => '符号',
    'column_name_nullable' => '可为NULL',
    'column_auto_increment' => '自增',
    'column_default' => '默认',
    'column_comment' => '注释',
    'column_auto_primary_key' => '键',
    'tab_new_table' => '新增表',
    'btn_add_column' => '新增列',
    'btn_delete_column' => '删除列',
    'btn_add_id' => '添加ID',
    'btn_add_timestamps' => '添加时间戳',
    'btn_add_soft_deleting' => '添加软删除支持',
    'id_exists' => '表中已存在ID列.',
    'timestamps_exist' => '表中已存在created_at列和deleted_at列.',
    'soft_deleting_exist' => '表中已存在deleted_at列.',
    'confirm_delete' => '删除表?',
    'error_enum_not_supported' => '表包含生成器当前不支持的类型为“enum”的列.',
    'error_table_name_invalid_prefix' => '表名应以插件前缀开头: \':prefix\'.',
    'error_table_name_invalid_characters' => '表名无效。表名只能包含拉丁字母、数字和下划线。名称应以拉丁字母开头，不能包含空格.',
    'error_table_duplicate_column' => '重复列名: \':column\'.',
    'error_table_auto_increment_in_compound_pk' => '自动递增列不能是复合主键的一部分.',
    'error_table_mutliple_auto_increment' => '表不能包含多个自动递增列.',
    'error_table_auto_increment_non_integer' => '自动递增列应具有整数类型.',
    'error_table_decimal_length' => ':type 的Length参数的格式应为“10,2”，不带空格.',
    'error_table_length' => ':type 长度参数应指定为整数.',
    'error_unsigned_type_not_int' => '\':column\' 列出错。无符号标志只能应用于整数类型列.',
    'error_integer_default_value' => '整数列 \':column\' 的默认值无效。允许的格式为“10”、“-10”.',
    'error_decimal_default_value' => '十进制或双精度列的默认值无效 \':column\'. 允许的格式为\'1.00\'，\'1.00\'.',
    'error_boolean_default_value' => '布尔列的默认值无效 \':column\'. 允许的值为“0”和“1”，或“true”和“false”.',
    'error_unsigned_negative_value' => '无符号列 \':column\' 的默认值不能为负.',
    'error_table_already_exists' => '数据库中已存在表 \':name\'.',
    'error_table_name_too_long' => '表名的长度不应超过64个字符.',
    'error_column_name_too_long' => '列名 \':column\' 太长。列名的长度不应超过64个字符.',
  ],
  'model' => [
    'menu_label' => '模型',
    'entity_name' => '模型',
    'no_records' => '未找到模型',
    'search' => '搜索...',
    'add' => '添加...',
    'forms' => '表单',
    'lists' => '列表',
    'field_class_name' => '类名',
    'field_database_table' => '数据库表',
    'field_add_timestamps' => '添加时间戳支持',
    'field_add_timestamps_description' => '数据库表必须存在 created_at 和 updated_at 字段.',
    'field_add_soft_deleting' => '添加软删除支持',
    'field_add_soft_deleting_description' => '数据库表必须存在 deleted_at 字段.',
    'error_class_name_exists' => '指定类名的模型文件已存在: :path',
    'error_timestamp_columns_must_exist' => '数据库表必须存在 created_at 和 updated_at 字段.',
    'error_deleted_at_column_must_exist' => '数据库表必须存在 deleted_at 字段.',
    'add_form' => '添加表单',
    'add_list' => '添加列表',
  ],
  'form' => [
    'saved' => '表单已保存',
    'confirm_delete' => '删除表单?',
    'tab_new_form' => '新表单',
    'btn_add_database_fields' => '添加数据库字段',
    'property_label_title' => '标签',
    'property_label_required' => '请指定控制标签.',
    'property_span_title' => '跨度',
    'property_comment_title' => '注释',
    'property_comment_above_title' => '在注释上',
    'property_default_title' => '默认',
    'property_checked_default_title' => '默认选中',
    'property_css_class_title' => 'CSS 类名',
    'property_css_class_description' => '分配给字段容器的可选CSS类.',
    'property_disabled_title' => '禁止',
    'property_read_only_title' => '仅读',
    'property_hidden_title' => '隐藏',
    'property_required_title' => '必填',
    'property_field_name_title' => '字段名',
    'property_placeholder_title' => '占位符',
    'property_default_from_title' => '默认来源',
    'property_stretch_title' => '伸展',
    'property_stretch_description' => '指定此字段是否拉伸以适合父级高度.',
    'property_context_title' => '上下文',
    'property_context_description' => '指定显示字段时应使用的窗体上下文.',
    'property_context_create' => '创建',
    'property_context_update' => '更新',
    'property_context_preview' => '预览',
    'property_dependson_title' => '依赖',
    'property_trigger_action' => '行为',
    'property_trigger_show' => '显示',
    'property_trigger_hide' => '隐藏',
    'property_trigger_enable' => '开启',
    'property_trigger_disable' => '禁止',
    'property_trigger_empty' => '空',
    'property_trigger_field' => '字段',
    'property_trigger_field_description' => '定义将触发操作的其他字段名.',
    'property_trigger_condition' => '条件',
    'property_trigger_condition_description' => '确定指定字段应满足的条件，以便将条件视为“true”。支持的值：选中、未选中、值[somevalue].',
    'property_trigger_condition_checked' => '已选中',
    'property_trigger_condition_unchecked' => '未选中',
    'property_trigger_condition_somevalue' => '值[在此处输入值]',
    'property_preset_title' => '预设',
    'property_preset_description' => '允许字段值最初由另一个字段的值设置，使用输入预置转换器进行转换.',
    'property_preset_field' => '字段',
    'property_preset_field_description' => '定义要从中获取值的其他字段名.',
    'property_preset_type' => '类型',
    'property_preset_type_description' => '指定转换类型',
    'property_attributes_title' => '属性',
    'property_attributes_description' => '要添加到表单字段元素的自定义HTML属性.',
    'property_container_attributes_title' => '容器属性',
    'property_container_attributes_description' => '要添加到表单域容器元素的自定义HTML属性.',
    'property_group_advanced' => '高级',
    'property_dependson_description' => '此字段所依赖的其他字段名的列表，当其他字段被修改时，此字段将更新。每行一个字段.',
    'property_trigger_title' => '触发',
    'property_trigger_description' => '允许根据其他元素的状态更改元素属性，如可见性或值.',
    'property_default_from_description' => '从另一个字段的值中获取默认值.',
    'property_field_name_required' => '字段名是必需的',
    'property_field_name_regex' => '字段名只能包含拉丁字母、数字、下划线、短划线和方括号.',
    'property_attributes_size' => '大小',
    'property_attributes_size_tiny' => '微小的',
    'property_attributes_size_small' => '小',
    'property_attributes_size_large' => '大',
    'property_attributes_size_huge' => '巨大的',
    'property_attributes_size_giant' => '特大的',
    'property_comment_position' => '注释位置',
    'property_comment_position_above' => '在上面',
    'property_comment_position_below' => '在下面',
    'property_hint_path' => '提示部分路径',
    'property_hint_path_description' => '包含提示文本的部分文件的路径。使用$符号来引用插件根目录，例如：$/acme/blog/partials/_hint.htm',
    'property_hint_path_required' => '请输入提示部分路径',
    'property_partial_path' => '部分路径',
    'property_partial_path_description' => '部分文件的路径。使用$符号来引用插件根目录，例如：$/acme/blog/partials/_partial.htm',
    'property_partial_path_required' => '请输入部分路径',
    'property_code_language' => '语言',
    'property_code_theme' => '主题',
    'property_theme_use_default' => '使用默认主题',
    'property_group_code_editor' => '代码编辑器',
    'property_gutter' => 'Gutter',
    'property_gutter_show' => '可见',
    'property_gutter_hide' => '隐藏',
    'property_wordwrap' => '自动换行',
    'property_wordwrap_wrap' => '换行',
    'property_wordwrap_nowrap' => '不换行',
    'property_fontsize' => '字体大小',
    'property_codefolding' => '代码目录',
    'property_codefolding_manual' => '指南',
    'property_codefolding_markbegin' => '标记开始',
    'property_codefolding_markbeginend' => '标记开始和结束',
    'property_autoclosing' => '自动关闭',
    'property_enabled' => '开启',
    'property_disabled' => '禁止',
    'property_soft_tabs' => '软标签',
    'property_tab_size' => '标签大小',
    'property_readonly' => '仅读',
    'property_use_default' => '使用默认设置',
    'property_options' => '选项',
    'property_prompt' => '提示',
    'property_prompt_description' => '“创建”按钮显示的文本.',
    'property_prompt_default' => '添加新项',
    'property_available_colors' => '可用颜色',
    'property_available_colors_description' => '十六进制格式的可用颜色列表（#FF0000）。将默认颜色集保留为空。每行输入一个值.',
    'property_datepicker_mode' => '模式',
    'property_datepicker_mode_date' => '日期',
    'property_datepicker_mode_datetime' => '日期时间',
    'property_datepicker_mode_time' => '时间',
    'property_datepicker_min_date' => '最小日期',
    'property_datepicker_min_date_description' => '可以选择的最小/最早日期。默认值为空（2000-01-01）.',
    'property_datepicker_max_date' => '最大日期',
    'property_datepicker_max_date_description' => '可选择的最大/最晚日期。将默认值留空（2020-12-31）.',
    'property_datepicker_date_invalid_format' => '无效的日期格式。使用格式YYYY-MM-DD.',
    'property_datepicker_year_range' => '年份范围',
    'property_datepicker_year_range_description' => '两边的年数（如10）或上下范围的数组（如[19002015]）。将默认值留空（10）.',
    'property_datepicker_year_range_invalid_format' => '年份范围格式无效。使用数字（如“10”）或上限/下限数组（如“[19002015]”）',
    'property_datepicker_format' => '格式',
    'property_datepicker_year_format_description' => '定义自定义日期格式。默认格式为“Y-m-d”',
    'property_fileupload_mode' => '模式',
    'property_fileupload_mode_file' => '文件',
    'property_fileupload_mode_image' => '图片',
    'property_group_fileupload' => '上传文件',
    'property_fileupload_image_width' => '图片宽度',
    'property_fileupload_image_width_description' => '可选参数-图像大小将调整为此宽度。仅适用于图像模式.',
    'property_fileupload_invalid_dimension' => '维度值无效-请输入一个数字.',
    'property_fileupload_image_height' => '图片高度',
    'property_fileupload_image_height_description' => '可选参数-图像大小将调整到此高度。仅适用于图像模式.',
    'property_fileupload_file_types' => '文件类型',
    'property_fileupload_file_types_description' => '上传可接受的文件扩展名的可选逗号分隔列表。如: zip,txt',
    'property_fileupload_mime_types' => 'MIME 类型',
    'property_fileupload_maxfilesize' => '最大文件大小',
    'property_fileupload_maxfilesize_description' => '上传者接受的文件大小（以 Mb 为单位），可选。',
    'property_fileupload_invalid_maxfilesize' => '最大文件大小值无效',
    'property_fileupload_maxfiles' => '最大文件数量',
    'property_fileupload_invalid_maxfiles' => '最大文件数量值无效',
    'property_fileupload_maxfiles_description' => '允许上传的最大文件数量',
    'property_fileupload_mime_types_description' => '上传接受的可选逗号分隔的MIME类型列表，可以是文件扩展名，也可以是完全限定名. 如: bin,txt',
    'property_fileupload_use_caption' => '使用说明',
    'property_fileupload_use_caption_description' => '允许为文件设置标题和说明.',
    'property_fileupload_thumb_options' => '缩略图选项',
    'property_fileupload_thumb_options_description' => '管理自动生成的缩略图的选项。仅适用于图像模式.',
    'property_fileupload_thumb_mode' => '模式',
    'property_fileupload_thumb_auto' => '自动',
    'property_fileupload_thumb_exact' => '扩展',
    'property_fileupload_thumb_portrait' => '纵向',
    'property_fileupload_thumb_landscape' => '横向',
    'property_fileupload_thumb_crop' => '裁切',
    'property_fileupload_thumb_extension' => '文件扩展名',
    'property_name_from' => '列名',
    'property_name_from_description' => '用于显示名称的关系列名.',
    'property_relation_select' => '选择',
    'property_relation_select_description' => '将多个列合并在一起以显示名称',
    'property_relation_scope' => '范围',
    'property_relation_scope_description' => '指定在相关表单模型中定义的查询范围方法，以始终应用于列表查询',
    'property_description_from' => '列描述',
    'property_description_from_description' => '用于显示说明的关系列名称.',
    'property_recordfinder_prompt' => '提示',
    'property_recordfinder_prompt_description' => '未选择记录时要显示的文本。%s字符表示搜索图标。为默认提示保留为空.',
    'property_recordfinder_list' => '列表配置',
    'property_recordfinder_list_description' => '对列表列定义文件的引用。使用$符号来引用插件根目录，例如：$/acme/blog/lists/_list.yaml',
    'property_recordfinder_list_required' => '请提供列表YAML文件的路径',
    'property_group_recordfinder' => '查找记录',
    'property_mediafinder_mode' => '模式',
    'property_mediafinder_mode_file' => '文件',
    'property_mediafinder_mode_image' => '图片',
    'property_mediafinder_image_width_description' => '如果使用图像类型，预览图像将按此宽度显示（可选）.',
    'property_mediafinder_image_height_description' => '如果使用图像类型，预览图像将显示到此高度（可选）.',
    'property_group_taglist' => '标签列表',
    'property_taglist_mode' => '模式',
    'property_taglist_mode_description' => '定义此字段的值作为返回格式',
    'property_taglist_mode_string' => '字符串',
    'property_taglist_mode_array' => '数组',
    'property_taglist_mode_relation' => '关联',
    'property_taglist_separator' => '分割符',
    'property_taglist_separator_comma' => '分割符号',
    'property_taglist_separator_space' => '空格',
    'property_taglist_options' => '预定义的标记',
    'property_taglist_custom_tags' => '自定义标签',
    'property_taglist_custom_tags_description' => '允许用户手动输入自定义标记.',
    'property_taglist_name_from' => '来源名称',
    'property_taglist_name_from_description' => '定义标记中显示的关系模型属性。仅用于“关联”模式.',
    'property_taglist_use_key' => '使用密钥',
    'property_taglist_use_key_description' => '如果选中，标记列表将使用键而不是值来保存和读取数据。仅用于“关联”模式.',
    'property_group_relation' => '关联',
    'property_relation_prompt' => '提示',
    'property_relation_prompt_description' => '没有可用选择时要显示的文本.',
    'property_empty_option' => '空选项',
    'property_empty_option_description' => 'empty选项对应于空选择，但与占位符不同，它可以重新选择.',
    'property_show_search' => '显示搜索',
    'property_show_search_description' => '启用此下拉列表的搜索功能.',
    'property_title_from' => '标题来自',
    'property_title_from_description' => '指定一个子字段名称以使用该字段的值作为每个转发器项目的标题。',
    'property_min_items' => '最小项',
    'property_min_items_description' => '转发器内允许的最小项目数。',
    'property_min_items_integer' => '最小项目必须是正整数。',
    'property_max_items' => '最大项',
    'property_max_items_description' => '转发器中允许的最大项目数。',
    'property_max_items_integer' => '最大项目必须是正整数。',
    'property_display_mode' => '风格',
    'property_display_mode_description' => '定义应用到这个转发器的行为。',
    'property_switch_label_on' => 'ON标题',
    'property_switch_label_on_description' => '为“ON”开关状态设置自定义标题',
    'property_switch_label_off' => 'OFF标题',
    'property_switch_label_off_description' => '为“OFF”开关状态设置自定义标题',
    'control_group_standard' => '标准',
    'control_group_widgets' => '控件',
    'click_to_add_control' => '添加控件',
    'loading' => '加载中...',
    'control_text' => '文本',
    'control_text_description' => '单行文本框',
    'control_password' => '密码',
    'control_password_description' => '单行密码文本字段',
    'control_checkbox' => '复选框',
    'control_checkbox_description' => '单选框',
    'control_switch' => '开关',
    'control_switch_description' => '单选开关盒，复选框的替代品',
    'control_textarea' => '文本域',
    'control_textarea_description' => '高度可控的多行文本框',
    'control_dropdown' => '下拉框',
    'control_dropdown_description' => '带有静态或动态选项的下拉列表',
    'control_balloon-selector' => '气球选择器',
    'control_balloon-selector_description' => '一次只能使用静态或动态选项选择一个项目的列表',
    'control_unknown' => '未知控件类型: :type',
    'control_repeater' => '循环组件',
    'control_repeater_description' => '输出一组重复的窗体控件',
    'control_number' => '数值',
    'control_number_description' => '只接受数字的单行文本框',
    'control_hint' => '提示',
    'control_hint_description' => '输出可由用户隐藏的框中的部分内容',
    'control_partial' => '部分',
    'control_partial_description' => '输出部分内容',
    'control_section' => '切片',
    'control_section_description' => '显示带有标题和副标题的窗体节',
    'control_radio' => '单选框列表',
    'control_radio_description' => '单选选项列表，一次只能选择一个项目',
    'control_radio_option_1' => '选项 1',
    'control_radio_option_2' => '选项 2',
    'control_checkboxlist' => '复选框列表',
    'control_checkboxlist_description' => '复选框列表，可在其中选择多个项目',
    'control_codeeditor' => '代码编辑器',
    'control_codeeditor_description' => '用于格式化代码或标记的纯文本编辑器',
    'control_colorpicker' => '颜色选择器',
    'control_colorpicker_description' => '用于选择十六进制颜色值的字段',
    'control_datepicker' => '日期选择器',
    'control_datepicker_description' => '用于选择日期和时间的文本字段',
    'control_richeditor' => '富文本编辑器',
    'control_richeditor_description' => '富格式文本的可视化编辑器，也称为所见即所得编辑器',
    'property_group_rich_editor' => '富文本编辑器',
    'property_richeditor_toolbar_buttons' => '工具栏按钮',
    'property_richeditor_toolbar_buttons_description' => '在编辑器工具栏上显示哪些按钮',
    'control_markdown' => 'Markdown 编辑器',
    'control_markdown_description' => '标记格式文本的基本编辑器',
    'control_taglist' => '标签列表',
    'control_taglist_description' => '用于输入标签列表的字段',
    'control_fileupload' => '文件上传',
    'control_fileupload_description' => '图像或常规文件的文件上传',
    'control_recordfinder' => '记录查找',
    'control_recordfinder_description' => '具有记录搜索功能的相关记录的详细信息的字段',
    'control_mediafinder' => '媒体查找器',
    'control_mediafinder_description' => '从媒体管理器库中选择项目的字段',
    'control_relation' => '关联',
    'control_relation_description' => '显示用于选择相关记录的下拉列表或复选框列表',
    'control_widget_type' => '小部件类型',
    'error_file_name_required' => '请输入表单文件名.',
    'error_file_name_invalid' => '文件名只能包含拉丁字母、数字、下划线、点和哈希.',
    'span_left' => '左边',
    'span_right' => '右边',
    'span_full' => '铺满',
    'span_auto' => '自动',
    'style_default' => '默认',
    'style_collapsed' => '折叠',
    'style_accordion' => '手风琴',
    'empty_tab' => '空标签',
    'confirm_close_tab' => '选项卡包含将被删除的控件。继续?',
    'tab' => '表单选项卡',
    'tab_title' => '标题',
    'controls' => '控件',
    'property_tab_title_required' => '选项卡标题不能为空.',
    'tabs_primary' => '主选项卡',
    'tabs_secondary' => '辅助选项卡',
    'tab_stretch' => '标准',
    'tab_stretch_description' => '指定此选项卡容器是否拉伸以适合父级高度.',
    'tab_css_class' => 'CSS 类',
    'tab_css_class_description' => '将CSS类分配给tabs容器.',
    'tab_name_template' => '选项卡 %s',
    'tab_already_exists' => '具有指定标题的选项卡已存在.',
  ],
  'list' => [
    'tab_new_list' => '新列表',
    'saved' => '列表已保存',
    'confirm_delete' => '删除列表?',
    'tab_columns' => '列',
    'btn_add_column' => '添加列',
    'btn_delete_column' => '删除列',
    'column_dbfield_label' => '字段',
    'column_dbfield_required' => '请输入模型字段',
    'column_name_label' => '标签',
    'column_label_required' => '请输入列标签',
    'column_type_label' => '类型',
    'column_type_required' => '请输入列类型',
    'column_type_text' => '文本',
    'column_type_number' => '数值',
    'column_type_switch' => '开关',
    'column_type_datetime' => '日期时间',
    'column_type_date' => '日期',
    'column_type_time' => '时间',
    'column_type_timesince' => '开始时间',
    'column_type_timetense' => '结束时间',
    'column_type_select' => '选择',
    'column_type_partial' => '部分',
    'column_label_default' => '默认',
    'column_label_searchable' => '搜索',
    'column_label_sortable' => '排序',
    'column_label_invisible' => '隐形的',
    'column_label_select' => '选择',
    'column_label_relation' => '关系',
    'column_label_css_class' => 'CSS 类',
    'column_label_width' => '宽度',
    'column_label_path' => '路径',
    'column_label_format' => '格式',
    'column_label_value_from' => '来源值',
    'error_duplicate_column' => '重复的列字段名: \':column\'.',
    'btn_add_database_columns' => '添加数据库列',
    'all_database_columns_exist' => '列表中已定义所有数据库列',
  ],
  'controller' => [
    'menu_label' => '控制器',
    'no_records' => '找不到插件控制器',
    'controller' => '控制器',
    'behaviors' => '行为',
    'new_controller' => '新控制器',
    'error_controller_has_no_behaviors' => '控制器没有可配置的行为.',
    'error_invalid_yaml_configuration' => '加载行为配置文件时出错: :file',
    'behavior_form_controller' => '窗体控制器行为',
    'behavior_form_controller_description' => '向后端页面添加表单功能。该行为提供了三个页面，分别称为Create、Update和Preview.',
    'property_behavior_form_placeholder' => '--选择表单--',
    'property_behavior_form_name' => '名称',
    'property_behavior_form_name_description' => '此窗体管理的对象的名称',
    'property_behavior_form_name_required' => '请输入表单名称',
    'property_behavior_form_file' => '表单配置',
    'property_behavior_form_file_description' => '对表单域定义文件的引用',
    'property_behavior_form_file_required' => '请输入表单配置文件的路径',
    'property_behavior_form_model_class' => '模型类',
    'property_behavior_form_model_class_description' => '一个模型类名，表单数据将根据此模型加载和保存.',
    'property_behavior_form_model_class_required' => '请选择一个模型类',
    'property_behavior_form_default_redirect' => '默认重定向',
    'property_behavior_form_default_redirect_description' => '保存或取消表单时默认重定向到的页面.',
    'property_behavior_form_create' => '创建记录页',
    'property_behavior_form_redirect' => '重定向',
    'property_behavior_form_redirect_description' => '创建记录时要重定向到的页.',
    'property_behavior_form_redirect_close' => '关闭重定向',
    'property_behavior_form_redirect_close_description' => '创建记录并随请求一起发送close post变量时要重定向到的页面.',
    'property_behavior_form_flash_save' => '保存闪存消息',
    'property_behavior_form_flash_save_description' => '保存记录时要显示的闪存消息.',
    'property_behavior_form_page_title' => '页面标题',
    'property_behavior_form_update' => '更新记录页',
    'property_behavior_form_update_redirect' => '重定向',
    'property_behavior_form_create_redirect_description' => '保存记录时要重定向到的页.',
    'property_behavior_form_flash_delete' => '删除闪存消息',
    'property_behavior_form_flash_delete_description' => '删除记录时显示的闪烁消息.',
    'property_behavior_form_preview' => '预览记录页',
    'behavior_list_controller' => '列表控制器行为',
    'behavior_list_controller_description' => '提供可排序和可搜索的列表，其记录上有可选链接。行为自动创建控制器操作“index”.',
    'property_behavior_list_title' => '列表标题',
    'property_behavior_list_title_required' => '请输入列表标题',
    'property_behavior_list_placeholder' => '--选择列表--',
    'property_behavior_list_model_class' => '模型类',
    'property_behavior_list_model_class_description' => '模型类名，列表数据从此模型加载.',
    'property_behavior_form_model_class_placeholder' => '--选择模型--',
    'property_behavior_list_model_class_required' => '请输入一个模型名称',
    'property_behavior_list_model_placeholder' => '--选择模型--',
    'property_behavior_list_file' => '列表配置文件',
    'property_behavior_list_file_description' => '对列表定义文件的引用',
    'property_behavior_list_file_required' => '请输入列表配置文件的路径',
    'property_behavior_list_record_url' => '记录 URL',
    'property_behavior_list_record_url_description' => '将每个列表记录链接到另一页。例：用户/更新：id：id部分替换为记录标识符.',
    'property_behavior_list_no_records_message' => '无记录消息',
    'property_behavior_list_no_records_message_description' => '找不到记录时要显示的消息',
    'property_behavior_list_recs_per_page' => '每一页记录',
    'property_behavior_list_recs_per_page_description' => '每页要显示的记录，使用0表示没有页。默认值：0',
    'property_behavior_list_recs_per_page_regex' => '每页记录数应为整数值',
    'property_behavior_list_show_setup' => '显示设置按钮',
    'property_behavior_list_show_sorting' => '显示排序',
    'property_behavior_list_default_sort' => '默认排序',
    'property_behavior_form_ds_column' => '列',
    'property_behavior_form_ds_direction' => '方向',
    'property_behavior_form_ds_asc' => '升序',
    'property_behavior_form_ds_desc' => '降序',
    'property_behavior_list_show_checkboxes' => '显示复选框',
    'property_behavior_list_onclick' => '点击处理程序',
    'property_behavior_list_onclick_description' => '单击记录时要执行的自定义JavaScript代码.',
    'property_behavior_list_show_tree' => '显示树',
    'property_behavior_list_show_tree_description' => '显示父/子记录的树层次结构.',
    'property_behavior_list_tree_expanded' => '树已展开',
    'property_behavior_list_tree_expanded_description' => '确定默认情况下是否应展开树节点.',
    'property_behavior_list_toolbar' => '工具栏',
    'property_behavior_list_toolbar_buttons' => '按钮部分',
    'property_behavior_list_toolbar_buttons_description' => '使用工具栏按钮引用控制器部分文件。例如：列表工具栏',
    'property_behavior_list_search' => '搜索',
    'property_behavior_list_search_prompt' => '搜索提示',
    'property_behavior_list_filter' => '筛选配置',
    'behavior_reorder_controller' => '重新排序控制器行为',
    'behavior_reorder_controller_description' => '提供对其记录进行排序和重新排序的功能。该行为会自动创建控制器操作“重新排序”.',
    'property_behavior_reorder_title' => '重新排序标题',
    'property_behavior_reorder_title_required' => '请输入重新排序标题',
    'property_behavior_reorder_name_from' => '属性名称',
    'property_behavior_reorder_name_from_description' => '应用作每个记录的标签的模型属性.',
    'property_behavior_reorder_name_from_required' => '请输入属性名称',
    'property_behavior_reorder_model_class' => '模型类',
    'property_behavior_reorder_model_class_description' => '模型类名，重新排序数据从此模型加载.',
    'property_behavior_reorder_model_class_placeholder' => '--选择模型--',
    'property_behavior_reorder_model_class_required' => '请选择模型类',
    'property_behavior_reorder_model_placeholder' => '--选择模型--',
    'property_behavior_reorder_toolbar' => '工具栏',
    'property_behavior_reorder_toolbar_buttons' => '按钮部分',
    'property_behavior_reorder_toolbar_buttons_description' => '使用工具栏按钮引用控制器部分文件。例如：重新排序工具栏',
    'error_controller_not_found' => '找不到原始控制器文件.',
    'error_invalid_config_file_name' => '行为 :class 配置文件名 (:file) 包含无效字符，造成不能假装.',
    'error_file_not_yaml' => '行为 :class 配置文件 (:file) 不是一个YAML文件. 仅支持YAML配置文件.',
    'saved' => '控制器已保存',
    'controller_name' => '控制器名称',
    'controller_name_description' => '控制器名称定义控制器后端页的类名和URL。应用标准的PHP变量命名约定。第一个符号应该是大写拉丁字母。示例：Build、News、Case.',
    'base_model_class' => '基本模型类',
    'base_model_class_description' => '选择要在需要或支持模型的行为中用作基础模型的模型类。您可以稍后配置这些行为.',
    'base_model_class_placeholder' => '--选择模型--',
    'controller_behaviors' => '行为',
    'controller_behaviors_description' => '选择控制器应该实现的行为。生成器将自动创建行为所需的视图文件.',
    'controller_permissions' => '权限',
    'controller_permissions_description' => '选择可以访问控制器视图的用户权限。权限可以在生成器的“权限”选项卡上定义。稍后可以在控制器PHP脚本中更改此选项.',
    'controller_permissions_no_permissions' => '插件没有定义任何权限.',
    'menu_item' => '活动菜单项',
    'menu_item_description' => '选择要激活控制器页面的菜单项。稍后可以在控制器PHP脚本中更改此选项.',
    'menu_item_placeholder' => '--选择菜单项--',
    'error_unknown_behavior' => '行为类 :class 没有在行为类库注册.',
    'error_behavior_view_conflict' => '所选行为提供了冲突的视图 (:view) 不能在控制器中一起使用.',
    'error_behavior_config_conflict' => '所选行为提供冲突的配置文件 (:file) 无法在控制器中一起使用.',
    'error_behavior_view_file_not_found' => '行为 :class 的视图模板 :view 没有找到.',
    'error_behavior_config_file_not_found' => '行为 :class 的配置文件 :file 没有找到.',
    'error_controller_exists' => '控制器文件已存在: :file.',
    'error_controller_name_invalid' => '无效格式的控制名称. 名称只能包含数字和拉丁字母。第一个符号应该是大写拉丁字母.',
    'error_behavior_view_file_exists' => '控制器视图文件已存在: :view.',
    'error_behavior_config_file_exists' => '行为配置文件已存在: :file.',
    'error_save_file' => '保存控制器文件错误: :file',
    'error_behavior_requires_base_model' => '行为 :behavior 必须选择一个基础模型类.',
    'error_model_doesnt_have_lists' => '所选模型没有任何列表。请先创建列表.',
    'error_model_doesnt_have_forms' => '所选模型没有任何窗体。请先创建表单.',
  ],
  'version' => [
    'menu_label' => '版本',
    'no_records' => '找不到插件版本',
    'search' => '搜索...',
    'tab' => '版本',
    'saved' => '已保存版本',
    'confirm_delete' => '删除版本?',
    'tab_new_version' => '新奔奔',
    'migration' => '迁移',
    'seeder' => '填充',
    'custom' => '增加版本号',
    'apply_version' => '应用版本',
    'applying' => '应用中...',
    'rollback_version' => '回滚版本',
    'rolling_back' => '回滚中...',
    'applied' => '应用的版本',
    'rolled_back' => '版本已回滚',
    'hint_save_unapplied' => '您保存了一个未应用的版本。当您或其他用户登录到后端或数据库表保存在生成器的数据库部分时，可以自动应用未应用的版本.',
    'hint_rollback' => '回滚某个版本也将回滚比此版本更新的所有版本。请注意，当您或其他用户登录到后端或数据库表保存在生成器的数据库部分时，系统会自动应用未应用的版本.',
    'hint_apply' => '应用一个版本也会应用插件的所有旧版本.',
    'dont_show_again' => '不再显示',
    'save_unapplied_version' => '保存未应用的版本',
    'sort_ascending' => '升序排序',
    'sort_descending' => '降序排序',
  ],
  'menu' => [
    'menu_label' => '后台菜单',
    'tab' => '菜单',
    'items' => '菜单选项',
    'saved' => '已保存菜单',
    'add_main_menu_item' => '添加主菜单项',
    'new_menu_item' => '菜单选项',
    'add_side_menu_item' => '添加子项',
    'side_menu_item' => '侧边栏菜单项',
    'property_label' => '标签',
    'property_label_required' => '请输入菜单项标签.',
    'property_url_required' => '请输入菜单项URL',
    'property_url' => 'URL',
    'property_icon' => '图标',
    'property_icon_required' => '请选择一个图标',
    'property_permissions' => '权限',
    'property_order' => '顺序',
    'property_order_invalid' => '请以整数值形式输入菜单项顺序。',
    'property_order_description' => '菜单项顺序管理其在菜单中的位置。如果没有提供订单，该项目将被放在菜单的末尾。默认订单值的增量为100.',
    'property_attributes' => 'HTML属性',
    'property_code' => '代码',
    'property_code_invalid' => '代码只能包含拉丁字母和数字',
    'property_code_required' => '请输入菜单项代码.',
    'error_duplicate_main_menu_code' => '主菜单项代码重复: \':code\'.',
    'error_duplicate_side_menu_code' => '重复的侧边栏菜单项代码: \':code\'.',
    'icon_svg' => 'SVG图标',
    'icon_svg_description' => '用于代替标准图标的SVG图标，SVG图标应该是一个矩形并且可以支持颜色',
    'counter' => '通知内容',
    'counter_description' => '要在菜单图标附近输出的数值。 该值应该是一个数字或一个返回数字的可调用对象',
    'counter_label' => '通知描述',
    'counter_label_description' => '一个字符串值，用于描述计数器中的数字引用',
    'counter_group' => '通知气泡',
  ],
  'localization' => [
    'menu_label' => '本地化',
    'language' => '语言',
    'strings' => '字符串',
    'confirm_delete' => '删除语言?',
    'tab_new_language' => '新语言',
    'no_records' => '未找到语言',
    'saved' => '语言文件已保存',
    'error_cant_load_file' => '无法加载请求的语言文件-找不到文件.',
    'error_bad_localization_file_contents' => '无法加载请求的语言文件。语言文件只能包含数组定义和字符串.',
    'error_file_not_array' => '无法加载请求的语言文件。语言文件应该返回一个数组.',
    'save_error' => '保存文件错误 \':name\'. 请检查写入权限.',
    'error_delete_file' => '删除本地化文件时出错.',
    'add_missing_strings' => '添加缺少的字符串',
    'copy' => '复制',
    'add_missing_strings_label' => '选择要从中复制缺少字符串的语言',
    'no_languages_to_copy_from' => '没有其他语言可用于复制字符串.',
    'new_string_warning' => '新字符串或节',
    'structure_mismatch' => '源语言文件的结构与正在编辑的文件的结构不匹配。编辑文件中的某些单独字符串与源文件中的节相对应（反之亦然），因此无法自动合并.',
    'create_string' => '创建新的字符串',
    'string_key_label' => '字符串键',
    'string_key_comment' => '使用句点作为节分隔符输入字符串键。例如：plugin.search. 字符串将在插件的默认语言本地化文件中创建.',
    'string_value' => '字符串值',
    'string_key_is_empty' => '字符串键不应为空',
    'string_key_is_a_string' => ':key 是字符串，不能包含其他字符串.',
    'string_value_is_empty' => '字符串值不应为空',
    'string_key_exists' => '字符串键已存在',
  ],
  'permission' => [
    'menu_label' => '权限',
    'tab' => '权限',
    'form_tab_permissions' => '权限',
    'btn_add_permission' => '添加权限',
    'btn_delete_permission' => '删除权限',
    'column_permission_label' => '权限代码',
    'column_permission_required' => '请输入权限代码',
    'column_tab_label' => '选项卡标题',
    'column_tab_required' => '请输入权限选项卡标题',
    'column_label_label' => '标签',
    'column_label_required' => '请输入选项卡标签',
    'saved' => '权限已保存',
    'error_duplicate_code' => '权限代码重复: \':code\'.',
  ],
  'yaml' => [
    'save_error' => '保存文件错误 \':name\'. 请检查写入权限.',
  ],
  'common' => [
    'error_file_exists' => '文件已存在: \':path\'.',
    'field_icon_description' => 'October 使用字体图标: http://octobercms.com/docs/ui/icon',
    'destination_dir_not_exists' => '目标目录不存在: \':path\'.',
    'error_make_dir' => '创建目录错误: \':name\'.',
    'error_dir_exists' => '目录已存在: \':path\'.',
    'template_not_found' => '模板文件未找到: \':name\'.',
    'error_generating_file' => '生成文件错误: \':path\'.',
    'error_loading_template' => '加载模板文件错误: \':name\'.',
    'select_plugin_first' => '请先选择一个插件。要查看插件列表，请单击左侧边栏上的>图标。.',
    'plugin_not_selected' => '未选择插件',
    'add' => '添加',
  ],
  'migration' => [
    'entity_name' => '迁移',
    'error_version_invalid' => '版本格式应参照：1.0.1',
    'field_version' => '版本',
    'field_description' => '描述',
    'field_code' => '代码',
    'save_and_apply' => '保存 & 应用',
    'error_version_exists' => '迁移版本已存在.',
    'error_script_filename_invalid' => '迁移脚本文件名只能包含拉丁字母、数字和下划线。名称应以拉丁字母开头，不能包含空格.',
    'error_cannot_change_version_number' => '无法更改应用版本的版本号.',
    'error_file_must_define_class' => '迁移代码应该定义迁移或种子类。如果只想更新版本号，请将“代码”字段留空.',
    'error_file_must_define_namespace' => '应该定义一个代码迁移。如果只想更新版本号，请将“代码”字段留空.',
    'no_changes_to_save' => '没有要保存的更改.',
    'error_namespace_mismatch' => '迁移代码应该使用插件名称空间: :namespace',
    'error_migration_file_exists' => '迁移文件 :file 已存在. 请使用其他的文件名称.',
    'error_cant_delete_applied' => '此版本已应用，无法删除。请先回滚版本.',
  ],
  'components' => [
    'list_title' => '记录列表',
    'list_description' => '显示选定模型的记录列表',
    'list_page_number' => '页数',
    'list_page_number_description' => '此值用于确定用户所在的页面.',
    'list_records_per_page' => '每页记录',
    'list_records_per_page_description' => '要在单个页面上显示的记录数。保留为空可禁用分页.',
    'list_records_per_page_validation' => '每页记录值的格式无效。值应为数字.',
    'list_no_records' => '无记录消息',
    'list_no_records_description' => '如果没有记录，则在列表中显示的消息。在默认组件的.',
    'list_no_records_default' => '找不到记录',
    'list_sort_column' => '列排序',
    'list_sort_column_description' => '记录排序依据的模型列',
    'list_sort_direction' => '方向',
    'list_display_column' => '显示列',
    'list_display_column_description' => '要在列表中显示的列。在默认组件的.',
    'list_display_column_required' => '请选择显示列.',
    'list_details_page' => '详情页',
    'list_details_page_description' => '显示记录详细信息的页面.',
    'list_details_page_no' => '--无详细信息页--',
    'list_sorting' => '排序',
    'list_pagination' => '分页',
    'list_order_direction_asc' => '升序',
    'list_order_direction_desc' => '倒叙',
    'list_model' => '模型类',
    'list_scope' => '范围',
    'list_scope_description' => '获取记录的可选模型范围',
    'list_scope_default' => '--选择范围，可选--',
    'list_scope_value' => '范围值',
    'list_scope_value_description' => '传递给模型范围的可选值',
    'list_details_page_link' => '链接到详细信息页',
    'list_details_key_column' => '详细信息键列',
    'list_details_key_column_description' => '要用作详细信息页链接中的记录标识符的模型列.',
    'list_details_url_parameter' => 'URL 参数名称',
    'list_details_url_parameter_description' => '采用记录标识符的详细信息页URL参数的名称.',
    'details_title' => '记录详情',
    'details_description' => '显示选定模型的记录详细信息',
    'details_model' => '模型类',
    'details_identifier_value' => '标识符值',
    'details_identifier_value_description' => '从数据库加载记录的标识符值。指定固定值或URL参数名称.',
    'details_identifier_value_required' => '标识符值是必需的',
    'details_key_column' => '键列',
    'details_key_column_description' => '要用作从数据库提取记录的记录标识符的模型列.',
    'details_key_column_required' => '键列名是必需的',
    'details_display_column' => '显示列',
    'details_display_column_description' => '要在详细信息页面上显示的模型列。在默认组件的.',
    'details_display_column_required' => '请选择显示列.',
    'details_not_found_message' => '未找到消息',
    'details_not_found_message_description' => '未找到记录时要显示的消息。在默认组件的.',
    'details_not_found_message_default' => '记录未找到',
  ],
  'validation' => [
    'reserved' => ':attribute 不能是PHP保留关键字',
  ],
];
