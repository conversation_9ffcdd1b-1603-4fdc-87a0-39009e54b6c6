v1.0.1:
    - Initialize plugin.
    - create_pdf_layouts_table.php
    - create_pdf_templates_table.php
v1.0.2: Minor changes.
v1.1.0: Important update.
v1.1.1: Use Twig::parse() facade. Only update for October build 300 and above.
v1.1.2: Minor changes.
v1.1.3: Add stream parameters and Czech locale. Thanks to @vojtasvoboda.
v1.1.4: Add Spanish and Spanish-Argentina locale. Thanks to @kocholes.
v1.1.5: UI improvements. Thanks to @kocholes.
v2.0.0: This is an important update that contains breaking changes.
v2.0.1: Add preview HTML buttons.
v2.1.0: This is an important update that contains breaking changes.
v2.1.1: Upgrade to DOMPDF 0.7.
v2.1.2: Database maintenance. Updated all timestamp columns to be nullable.
v2.1.3: Upgrade to DOMPDF 0.8.
v2.1.4: German language. Thanks to @TimFoerster.
v2.1.5: Fix open_basedir restriction on some hostings. Set isRemoteEnabled flag to allow absolute paths.
v2.1.6: Allow Laravel 5.5.
v3.0.0: Add support for Laravel 5.5.
v3.0.1: Add support for RainLab.Translate plugin.
v3.0.2: Fix creating fonts directory.
v3.0.3: Allow to use CMS partials and filters.
v3.0.4:
    - Add paper size and orientation configuration.
    - add_paper_configuration_to_templates_table.php
v3.0.5: Support LESS in PDF layouts.
v4.0.0: Require PHP 7.1. Please see upgrade guide before update.
v4.0.1: Fix error when active theme is not set.
v4.0.2:
    - Allow to register mail templates and layouts.
    - add_is_custom_to_templates_table.php
    - add_is_locked_to_layouts_table.php
v4.0.3:
    - Fix custom templates error.
    - fix_custom_templates_error.php
v4.0.4: Fix composer installation.
v4.0.5: Fix custom template error.
v4.0.6: Allow to reset view template to default.
v4.0.7: Add Polish translation.
v4.0.8: Allow to set dompdf option with dynamic method.
v5.0.1: Support October v2.0 and upgrade to DOMPDF 1.0.
v5.0.2: Add demo console command.
v5.0.3: Update documentation.
v5.0.4: Fix preview pdf layout.
v5.0.5: Brazilian portuguese language. Thanks to @gcairesdev.
v6.0.0: Require October CMS 3.0.
v6.0.1: Minor update.
v7.0.0: Update to Laravel Dompdf v2.0.
v7.0.1: Fix page numbers demo.
v7.1.0: Fix creating public directory.
v7.1.1: Minor fix.
v7.1.2: Allow self signed certificates on development environment.
