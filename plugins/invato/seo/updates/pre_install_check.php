<?php

namespace Invato\Seo\updates;

use October\Rain\Database\Updates\Seeder;
use System\Classes\UpdateManager;
use System\Models\PluginVersion;

class PreInstallCheck extends Seeder
{
    public function run()
    {
        $blogPlugin = 'Invato.Blog';
        $agendaPlugin = 'Invato.Agenda';
        $careersPlugin = 'Invato.Careers';
        $portfolioPlugin = 'Invato.Portfolio';

        if (
            ! PluginVersion::query()
                ->where('code', '=', $blogPlugin)
                ->exists()
            &&
            ! PluginVersion::query()
                ->where('code', '=', $agendaPlugin)
                ->exists()
            &&
            ! PluginVersion::query()
                ->where('code', '=', $careersPlugin)
                ->exists()
            &&
            ! PluginVersion::query()
                ->where('code', '=', $portfolioPlugin)
                ->exists()
        ) {
            $manager = UpdateManager::instance();
            $manager->migratePlugin($blogPlugin);
            $manager->migratePlugin($agendaPlugin);
            $manager->migratePlugin($careersPlugin);
            $manager->migratePlugin($portfolioPlugin);
        }
    }
}
