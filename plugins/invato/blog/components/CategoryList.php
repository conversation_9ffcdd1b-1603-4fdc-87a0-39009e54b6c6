<?php namespace Invato\Blog\Components;

use Cms\Classes\ComponentBase;
use Cms\Classes\Page;
use Invato\Blog\Models\BlogSetting;
use Invato\Blog\Models\Category;

/**
 * CategoryList Component
 *
 * @link https://docs.octobercms.com/3.x/extend/cms-components.html
 */
class CategoryList extends ComponentBase
{
    public $categories;
    public $categoryPage;

    public function componentDetails()
    {
        return [
            'name' => 'invato.blog::lang.categorylist.name',
            'description' => 'invato.blog::lang.categorylist.description'
        ];
    }

    /**
     * @link https://docs.octobercms.com/3.x/element/inspector-types.html
     */
    public function defineProperties()
    {
        return [
            'showImages' => [
                'title' => 'Show images',
                'type' => 'checkbox',
                'default' => false
            ],
        ];
    }

    //
    // Rendering and processing
    //
    public function onRun()
    {
        $this->categories = Category::all()->toNested();
        $this->blogPage = BlogSetting::get('blogPage');
        $this->postPage = BlogSetting::get('postPage');
        $this->categoryPage = BlogSetting::get('categoryPage');
        $this->showAuthor = BlogSetting::get('showAuthor');
        $this->showDates = BlogSetting::get('showDates');
    }

    public function getCategoryPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }
}
