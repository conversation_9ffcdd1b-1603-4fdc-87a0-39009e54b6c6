<?php namespace Invato\Teams\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoTeamsDepartments2 extends Migration
{
    public function up()
    {
        Schema::table('invato_teams_departments', function($table)
        {
            $table->integer('parent_id')->nullable()->change();
        });
    }
    
    public function down()
    {
        Schema::table('invato_teams_departments', function($table)
        {
            $table->integer('parent_id')->nullable(false)->change();
        });
    }
}
