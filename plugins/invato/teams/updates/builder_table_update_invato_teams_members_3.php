<?php namespace Invato\Teams\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoTeamsMembers3 extends Migration
{
    public function up()
    {
        Schema::table('invato_teams_members', function($table)
        {
            $table->integer('parent_id')->nullable()->change();
        });
    }
    
    public function down()
    {
        Schema::table('invato_teams_members', function($table)
        {
            $table->integer('parent_id')->nullable(false)->change();
        });
    }
}
